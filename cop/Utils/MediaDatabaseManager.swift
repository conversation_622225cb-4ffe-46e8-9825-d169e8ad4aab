//
//  MediaDatabaseManager.swift
//  cop
//
//  Created by Augment Agent on 2025-06-18.
//  OPTIMIZED: SQLite数据库管理器，替代UserDefaults存储
//

import Foundation
import SQLite3
import os.log

// MARK: - 媒体数据库管理器
class MediaDatabaseManager {
    static let shared = MediaDatabaseManager()
    
    private var db: OpaquePointer?
    private let dbQueue = DispatchQueue(label: "media.database", qos: .utility)
    private let logger = Logger(subsystem: "com.cop.app", category: "MediaDatabase")
    
    private init() {
        Task {
            await setupDatabase()
        }
    }
    
    // MARK: - OPTIMIZED: 数据库初始化
    private func setupDatabase() async {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let dbPath = documentsPath.appendingPathComponent("MediaLibrary.sqlite").path
        
        if sqlite3_open(dbPath, &db) == SQLITE_OK {
            let createTablesSQL = """
                -- 媒体文件表
                CREATE TABLE IF NOT EXISTS media_files (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    file_size INTEGER NOT NULL,
                    creation_date TIMESTAMP NOT NULL,
                    modification_date TIMESTAMP NOT NULL,
                    local_url TEXT NOT NULL,
                    thumbnail_url TEXT,
                    folder_path TEXT NOT NULL,
                    duration REAL,
                    width INTEGER,
                    height INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                -- 文件夹表
                CREATE TABLE IF NOT EXISTS folders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    path TEXT NOT NULL UNIQUE,
                    media_count INTEGER DEFAULT 0,
                    file_count INTEGER DEFAULT 0,
                    total_size INTEGER DEFAULT 0,
                    last_modified TIMESTAMP NOT NULL,
                    thumbnail_url TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                -- 导入历史表
                CREATE TABLE IF NOT EXISTS import_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    folder_name TEXT NOT NULL,
                    file_count INTEGER NOT NULL,
                    total_size INTEGER NOT NULL,
                    import_date TIMESTAMP NOT NULL,
                    status TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                -- 搜索索引
                CREATE INDEX IF NOT EXISTS idx_media_name ON media_files(name);
                CREATE INDEX IF NOT EXISTS idx_media_type ON media_files(type);
                CREATE INDEX IF NOT EXISTS idx_media_folder ON media_files(folder_path);
                CREATE INDEX IF NOT EXISTS idx_media_creation_date ON media_files(creation_date);
                CREATE INDEX IF NOT EXISTS idx_folder_name ON folders(name);
                CREATE INDEX IF NOT EXISTS idx_folder_modified ON folders(last_modified);
                
                -- 全文搜索支持
                CREATE VIRTUAL TABLE IF NOT EXISTS media_search USING fts5(
                    id UNINDEXED,
                    name,
                    folder_path,
                    content='media_files',
                    content_rowid='rowid'
                );
                
                -- 触发器：维护搜索索引
                CREATE TRIGGER IF NOT EXISTS media_search_insert AFTER INSERT ON media_files BEGIN
                    INSERT INTO media_search(id, name, folder_path) VALUES (new.id, new.name, new.folder_path);
                END;
                
                CREATE TRIGGER IF NOT EXISTS media_search_delete AFTER DELETE ON media_files BEGIN
                    DELETE FROM media_search WHERE id = old.id;
                END;
                
                CREATE TRIGGER IF NOT EXISTS media_search_update AFTER UPDATE ON media_files BEGIN
                    DELETE FROM media_search WHERE id = old.id;
                    INSERT INTO media_search(id, name, folder_path) VALUES (new.id, new.name, new.folder_path);
                END;
            """
            
            if sqlite3_exec(db, createTablesSQL, nil, nil, nil) == SQLITE_OK {
                logger.info("✅ 媒体数据库初始化成功")
            } else {
                logger.error("❌ 数据库初始化失败")
            }
        } else {
            logger.error("❌ 无法打开数据库")
        }
    }
    
    // MARK: - OPTIMIZED: 批量插入媒体文件
    func insertMediaFiles(_ mediaFiles: [MediaFileInfo]) async throws {
        return try await withCheckedThrowingContinuation { continuation in
            dbQueue.async {
                let insertSQL = """
                    INSERT OR REPLACE INTO media_files 
                    (id, name, type, file_size, creation_date, modification_date, local_url, 
                     thumbnail_url, folder_path, duration, width, height, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """
                
                var statement: OpaquePointer?
                
                // 开始事务
                sqlite3_exec(self.db, "BEGIN TRANSACTION", nil, nil, nil)
                
                if sqlite3_prepare_v2(self.db, insertSQL, -1, &statement, nil) == SQLITE_OK {
                    for mediaFile in mediaFiles {
                        sqlite3_bind_text(statement, 1, mediaFile.id.uuidString, -1, nil)
                        sqlite3_bind_text(statement, 2, mediaFile.name, -1, nil)
                        sqlite3_bind_text(statement, 3, mediaFile.type.rawValue, -1, nil)
                        sqlite3_bind_int64(statement, 4, mediaFile.fileSize)
                        sqlite3_bind_double(statement, 5, mediaFile.creationDate.timeIntervalSince1970)
                        sqlite3_bind_double(statement, 6, mediaFile.modificationDate.timeIntervalSince1970)
                        sqlite3_bind_text(statement, 7, mediaFile.localURL.path, -1, nil)
                        
                        if let thumbnailURL = mediaFile.thumbnailURL {
                            sqlite3_bind_text(statement, 8, thumbnailURL.path, -1, nil)
                        } else {
                            sqlite3_bind_null(statement, 8)
                        }
                        
                        sqlite3_bind_text(statement, 9, mediaFile.folderPath, -1, nil)
                        
                        if let duration = mediaFile.duration {
                            sqlite3_bind_double(statement, 10, duration)
                        } else {
                            sqlite3_bind_null(statement, 10)
                        }
                        
                        if let dimensions = mediaFile.dimensions {
                            sqlite3_bind_int(statement, 11, Int32(dimensions.width))
                            sqlite3_bind_int(statement, 12, Int32(dimensions.height))
                        } else {
                            sqlite3_bind_null(statement, 11)
                            sqlite3_bind_null(statement, 12)
                        }
                        
                        if sqlite3_step(statement) != SQLITE_DONE {
                            self.logger.error("插入媒体文件失败: \(mediaFile.name)")
                        }
                        
                        sqlite3_reset(statement)
                    }
                    
                    // 提交事务
                    sqlite3_exec(self.db, "COMMIT", nil, nil, nil)
                    
                    self.logger.info("✅ 批量插入 \(mediaFiles.count) 个媒体文件")
                    continuation.resume()
                } else {
                    sqlite3_exec(self.db, "ROLLBACK", nil, nil, nil)
                    continuation.resume(throwing: DatabaseError.insertFailed)
                }
                
                sqlite3_finalize(statement)
            }
        }
    }
    
    // MARK: - OPTIMIZED: 高性能搜索
    func searchMediaFiles(query: String, limit: Int = 100) async -> [MediaFileInfo] {
        return await withCheckedContinuation { continuation in
            dbQueue.async {
                var results: [MediaFileInfo] = []
                
                // 使用FTS5全文搜索
                let searchSQL = """
                    SELECT m.* FROM media_files m
                    JOIN media_search s ON m.id = s.id
                    WHERE media_search MATCH ?
                    ORDER BY rank
                    LIMIT ?
                """
                
                var statement: OpaquePointer?
                if sqlite3_prepare_v2(self.db, searchSQL, -1, &statement, nil) == SQLITE_OK {
                    sqlite3_bind_text(statement, 1, query, -1, nil)
                    sqlite3_bind_int(statement, 2, Int32(limit))
                    
                    while sqlite3_step(statement) == SQLITE_ROW {
                        if let mediaFile = self.parseMediaFileFromRow(statement) {
                            results.append(mediaFile)
                        }
                    }
                }
                
                sqlite3_finalize(statement)
                continuation.resume(returning: results)
            }
        }
    }
    
    // MARK: - OPTIMIZED: 分页加载
    func loadMediaFiles(offset: Int, limit: Int, sortBy: String = "creation_date", ascending: Bool = false) async -> [MediaFileInfo] {
        return await withCheckedContinuation { continuation in
            dbQueue.async {
                var results: [MediaFileInfo] = []
                let order = ascending ? "ASC" : "DESC"
                
                let selectSQL = """
                    SELECT * FROM media_files 
                    ORDER BY \(sortBy) \(order)
                    LIMIT ? OFFSET ?
                """
                
                var statement: OpaquePointer?
                if sqlite3_prepare_v2(self.db, selectSQL, -1, &statement, nil) == SQLITE_OK {
                    sqlite3_bind_int(statement, 1, Int32(limit))
                    sqlite3_bind_int(statement, 2, Int32(offset))
                    
                    while sqlite3_step(statement) == SQLITE_ROW {
                        if let mediaFile = self.parseMediaFileFromRow(statement) {
                            results.append(mediaFile)
                        }
                    }
                }
                
                sqlite3_finalize(statement)
                continuation.resume(returning: results)
            }
        }
    }
    
    // MARK: - 辅助方法
    private func parseMediaFileFromRow(_ statement: OpaquePointer?) -> MediaFileInfo? {
        guard let statement = statement else { return nil }
        
        let idString = String(cString: sqlite3_column_text(statement, 0))
        guard let id = UUID(uuidString: idString) else { return nil }
        
        let name = String(cString: sqlite3_column_text(statement, 1))
        let typeString = String(cString: sqlite3_column_text(statement, 2))
        guard let type = MediaType(rawValue: typeString) else { return nil }
        
        let fileSize = sqlite3_column_int64(statement, 3)
        let creationDate = Date(timeIntervalSince1970: sqlite3_column_double(statement, 4))
        let modificationDate = Date(timeIntervalSince1970: sqlite3_column_double(statement, 5))
        let localURL = URL(fileURLWithPath: String(cString: sqlite3_column_text(statement, 6)))
        
        var thumbnailURL: URL?
        if sqlite3_column_type(statement, 7) != SQLITE_NULL {
            thumbnailURL = URL(fileURLWithPath: String(cString: sqlite3_column_text(statement, 7)))
        }
        
        let folderPath = String(cString: sqlite3_column_text(statement, 8))
        
        var duration: TimeInterval?
        if sqlite3_column_type(statement, 9) != SQLITE_NULL {
            duration = sqlite3_column_double(statement, 9)
        }
        
        var dimensions: CGSize?
        if sqlite3_column_type(statement, 10) != SQLITE_NULL && sqlite3_column_type(statement, 11) != SQLITE_NULL {
            let width = sqlite3_column_int(statement, 10)
            let height = sqlite3_column_int(statement, 11)
            dimensions = CGSize(width: CGFloat(width), height: CGFloat(height))
        }
        
        return MediaFileInfo(
            id: id,
            name: name,
            type: type,
            fileSize: fileSize,
            creationDate: creationDate,
            modificationDate: modificationDate,
            localURL: localURL,
            thumbnailURL: thumbnailURL,
            folderPath: folderPath,
            duration: duration,
            dimensions: dimensions
        )
    }
    
    deinit {
        sqlite3_close(db)
    }
}

// MARK: - 数据库错误类型
enum DatabaseError: Error {
    case insertFailed
    case queryFailed
    case connectionFailed
}
