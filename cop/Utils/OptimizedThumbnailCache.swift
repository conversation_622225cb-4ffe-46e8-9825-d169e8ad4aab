//
//  OptimizedThumbnailCache.swift
//  cop
//
//  Created by Augment Agent on 2025-06-18.
//  OPTIMIZED: 三级缓存体系 - 内存→SQLite→文件系统
//

import Foundation
import UIKit
import SQLite3
import os.log

// MARK: - 优化的三级缓存系统
class OptimizedThumbnailCache {
    static let shared = OptimizedThumbnailCache()
    
    private let logger = Logger(subsystem: "com.cop.app", category: "ThumbnailCache")
    
    // OPTIMIZED: L1 - 内存缓存 (NSCache)
    private let memoryCache = NSCache<NSString, UIImage>()
    
    // OPTIMIZED: L2 - SQLite缓存
    private let databaseCache: SQLiteThumbnailCache
    
    // OPTIMIZED: L3 - 文件系统缓存
    private let fileSystemCache: FileSystemThumbnailCache
    
    // 缓存配置
    private let memoryCacheLimit = 50 * 1024 * 1024 // 50MB内存缓存
    private let maxMemoryItems = 200 // 最多200个缩略图
    
    private init() {
        setupMemoryCache()
        databaseCache = SQLiteThumbnailCache()
        fileSystemCache = FileSystemThumbnailCache()
        
        logger.info("🚀 三级缓存系统初始化完成")
    }
    
    // MARK: - OPTIMIZED: 智能缓存加载
    func loadThumbnail(for mediaFileID: UUID, size: CGSize = CGSize(width: 300, height: 300)) async -> UIImage? {
        let cacheKey = "\(mediaFileID.uuidString)_\(Int(size.width))x\(Int(size.height))"
        
        // L1: 内存缓存命中 (最快 ~1ms)
        if let cachedImage = memoryCache.object(forKey: cacheKey as NSString) {
            logger.debug("✅ L1缓存命中: \(cacheKey)")
            return cachedImage
        }
        
        // L2: SQLite缓存命中 (中等速度 ~5-10ms)
        if let imageData = await databaseCache.loadThumbnail(id: mediaFileID, size: size) {
            if let image = UIImage(data: imageData) {
                // 回填L1缓存
                memoryCache.setObject(image, forKey: cacheKey as NSString)
                logger.debug("✅ L2缓存命中: \(cacheKey)")
                return image
            }
        }
        
        // L3: 文件系统缓存 (较慢 ~20-50ms)
        if let image = await fileSystemCache.loadThumbnail(id: mediaFileID, size: size) {
            // 回填L1和L2缓存
            memoryCache.setObject(image, forKey: cacheKey as NSString)
            
            if let imageData = image.jpegData(compressionQuality: 0.8) {
                await databaseCache.storeThumbnail(imageData, id: mediaFileID, size: size)
            }
            
            logger.debug("✅ L3缓存命中: \(cacheKey)")
            return image
        }
        
        logger.debug("❌ 缓存未命中: \(cacheKey)")
        return nil
    }
    
    // MARK: - OPTIMIZED: 智能缓存存储
    func storeThumbnail(_ image: UIImage, for mediaFileID: UUID, size: CGSize = CGSize(width: 300, height: 300)) async {
        let cacheKey = "\(mediaFileID.uuidString)_\(Int(size.width))x\(Int(size.height))"
        
        // 存储到L1缓存
        memoryCache.setObject(image, forKey: cacheKey as NSString)
        
        // 异步存储到L2和L3缓存
        Task.detached(priority: .utility) {
            guard let imageData = image.jpegData(compressionQuality: 0.8) else { return }
            
            // 存储到L2缓存
            await self.databaseCache.storeThumbnail(imageData, id: mediaFileID, size: size)
            
            // 存储到L3缓存
            await self.fileSystemCache.storeThumbnail(imageData, id: mediaFileID, size: size)
            
            self.logger.debug("💾 缓存存储完成: \(cacheKey)")
        }
    }
    
    // MARK: - OPTIMIZED: 预加载策略
    func preloadThumbnails(for mediaFileIDs: [UUID], size: CGSize = CGSize(width: 300, height: 300)) async {
        logger.info("🔄 开始预加载 \(mediaFileIDs.count) 个缩略图")

        await withTaskGroup(of: Void.self) { group in
            let semaphore = AsyncSemaphore(value: 3) // 限制并发数

            for mediaFileID in mediaFileIDs {
                group.addTask {
                    await semaphore.wait()

                    // 只预加载不在L1缓存中的项目
                    let cacheKey = "\(mediaFileID.uuidString)_\(Int(size.width))x\(Int(size.height))"
                    if self.memoryCache.object(forKey: cacheKey as NSString) == nil {
                        _ = await self.loadThumbnail(for: mediaFileID, size: size)
                    }

                    await semaphore.signal()
                }
            }
        }

        logger.info("✅ 预加载完成")
    }
    
    // MARK: - 缓存管理
    func clearMemoryCache() async {
        memoryCache.removeAllObjects()
        logger.info("🧹 L1内存缓存已清理")
    }
    
    func clearAllCaches() async {
        memoryCache.removeAllObjects()
        await databaseCache.clearCache()
        await fileSystemCache.clearCache()
        logger.info("🧹 所有缓存已清理")
    }
    
    func getCacheStatistics() async -> CacheStatistics {
        let memoryCount = memoryCache.totalCostLimit
        let databaseCount = await databaseCache.getThumbnailCount()
        let fileSystemCount = await fileSystemCache.getThumbnailCount()
        
        return CacheStatistics(
            memoryItems: memoryCount,
            databaseItems: databaseCount,
            fileSystemItems: fileSystemCount
        )
    }
    
    // MARK: - 私有方法
    private func setupMemoryCache() {
        memoryCache.totalCostLimit = memoryCacheLimit
        memoryCache.countLimit = maxMemoryItems
        
        // 设置缓存清理策略
        memoryCache.evictsObjectsWithDiscardedContent = true
        
        // 监听内存警告
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { _ in
            Task { await self.clearMemoryCache() }
        }
    }
}

// MARK: - SQLite缓存实现
class SQLiteThumbnailCache {
    private var db: OpaquePointer?
    private let dbQueue = DispatchQueue(label: "sqlite.thumbnail.cache", qos: .utility)
    private let logger = Logger(subsystem: "com.cop.app", category: "SQLiteCache")
    
    init() {
        Task {
            await setupDatabase()
        }
    }
    
    private func setupDatabase() async {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let dbPath = documentsPath.appendingPathComponent("ThumbnailCache.sqlite").path
        
        if sqlite3_open(dbPath, &db) == SQLITE_OK {
            let createTableSQL = """
                CREATE TABLE IF NOT EXISTS thumbnails (
                    id TEXT PRIMARY KEY,
                    media_file_id TEXT NOT NULL,
                    width INTEGER NOT NULL,
                    height INTEGER NOT NULL,
                    data BLOB NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                
                CREATE INDEX IF NOT EXISTS idx_media_file_size ON thumbnails(media_file_id, width, height);
                CREATE INDEX IF NOT EXISTS idx_accessed_at ON thumbnails(accessed_at);
            """
            
            if sqlite3_exec(db, createTableSQL, nil, nil, nil) == SQLITE_OK {
                logger.info("✅ SQLite缓存数据库初始化成功")
            } else {
                logger.error("❌ SQLite数据库初始化失败")
            }
        }
    }
    
    func loadThumbnail(id: UUID, size: CGSize) async -> Data? {
        return await withCheckedContinuation { continuation in
            dbQueue.async {
                let cacheKey = "\(id.uuidString)_\(Int(size.width))x\(Int(size.height))"
                let selectSQL = "SELECT data FROM thumbnails WHERE id = ? LIMIT 1"
                
                var statement: OpaquePointer?
                if sqlite3_prepare_v2(self.db, selectSQL, -1, &statement, nil) == SQLITE_OK {
                    sqlite3_bind_text(statement, 1, cacheKey, -1, nil)
                    
                    if sqlite3_step(statement) == SQLITE_ROW {
                        let dataLength = sqlite3_column_bytes(statement, 0)
                        let dataPointer = sqlite3_column_blob(statement, 0)
                        
                        if let dataPointer = dataPointer {
                            let data = Data(bytes: dataPointer, count: Int(dataLength))
                            
                            // 更新访问时间
                            self.updateAccessTime(cacheKey: cacheKey)
                            
                            continuation.resume(returning: data)
                        } else {
                            continuation.resume(returning: nil)
                        }
                    } else {
                        continuation.resume(returning: nil)
                    }
                }
                
                sqlite3_finalize(statement)
            }
        }
    }
    
    func storeThumbnail(_ data: Data, id: UUID, size: CGSize) async {
        let cacheKey = "\(id.uuidString)_\(Int(size.width))x\(Int(size.height))"
        
        dbQueue.async {
            let insertSQL = """
                INSERT OR REPLACE INTO thumbnails (id, media_file_id, width, height, data, created_at, accessed_at)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """
            
            var statement: OpaquePointer?
            if sqlite3_prepare_v2(self.db, insertSQL, -1, &statement, nil) == SQLITE_OK {
                sqlite3_bind_text(statement, 1, cacheKey, -1, nil)
                sqlite3_bind_text(statement, 2, id.uuidString, -1, nil)
                sqlite3_bind_int(statement, 3, Int32(size.width))
                sqlite3_bind_int(statement, 4, Int32(size.height))
                
                data.withUnsafeBytes { bytes in
                    sqlite3_bind_blob(statement, 5, bytes.baseAddress, Int32(data.count), nil)
                }
                
                if sqlite3_step(statement) == SQLITE_DONE {
                    self.logger.debug("💾 SQLite缓存存储成功: \(cacheKey)")
                }
            }
            
            sqlite3_finalize(statement)
        }
    }
    
    func clearCache() async {
        dbQueue.async {
            let deleteSQL = "DELETE FROM thumbnails"
            sqlite3_exec(self.db, deleteSQL, nil, nil, nil)
            self.logger.info("🧹 SQLite缓存已清理")
        }
    }
    
    func getThumbnailCount() async -> Int {
        return await withCheckedContinuation { continuation in
            dbQueue.async {
                let countSQL = "SELECT COUNT(*) FROM thumbnails"
                var statement: OpaquePointer?
                var count = 0
                
                if sqlite3_prepare_v2(self.db, countSQL, -1, &statement, nil) == SQLITE_OK {
                    if sqlite3_step(statement) == SQLITE_ROW {
                        count = Int(sqlite3_column_int(statement, 0))
                    }
                }
                
                sqlite3_finalize(statement)
                continuation.resume(returning: count)
            }
        }
    }
    
    private func updateAccessTime(cacheKey: String) {
        let updateSQL = "UPDATE thumbnails SET accessed_at = CURRENT_TIMESTAMP WHERE id = ?"
        var statement: OpaquePointer?
        
        if sqlite3_prepare_v2(db, updateSQL, -1, &statement, nil) == SQLITE_OK {
            sqlite3_bind_text(statement, 1, cacheKey, -1, nil)
            sqlite3_step(statement)
        }
        
        sqlite3_finalize(statement)
    }
    
    deinit {
        sqlite3_close(db)
    }
}

// MARK: - 文件系统缓存实现
class FileSystemThumbnailCache {
    private let fileManager = FileManager.default
    private let logger = Logger(subsystem: "com.cop.app", category: "FileSystemCache")
    
    private var cacheDirectory: URL {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        return documentsPath.appendingPathComponent("Thumbnails")
    }
    
    func loadThumbnail(id: UUID, size: CGSize) async -> UIImage? {
        let fileName = "\(id.uuidString)_\(Int(size.width))x\(Int(size.height)).jpg"
        let fileURL = cacheDirectory.appendingPathComponent(fileName)
        
        guard fileManager.fileExists(atPath: fileURL.path) else {
            return nil
        }
        
        do {
            let data = try Data(contentsOf: fileURL)
            return UIImage(data: data)
        } catch {
            logger.error("文件系统缓存读取失败: \(error)")
            return nil
        }
    }
    
    func storeThumbnail(_ data: Data, id: UUID, size: CGSize) async {
        let fileName = "\(id.uuidString)_\(Int(size.width))x\(Int(size.height)).jpg"
        let fileURL = cacheDirectory.appendingPathComponent(fileName)
        
        do {
            try fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
            try data.write(to: fileURL)
        } catch {
            logger.error("文件系统缓存写入失败: \(error)")
        }
    }
    
    func clearCache() async {
        do {
            if fileManager.fileExists(atPath: cacheDirectory.path) {
                try fileManager.removeItem(at: cacheDirectory)
            }
            try fileManager.createDirectory(at: cacheDirectory, withIntermediateDirectories: true)
            logger.info("🧹 文件系统缓存已清理")
        } catch {
            logger.error("文件系统缓存清理失败: \(error)")
        }
    }
    
    func getThumbnailCount() async -> Int {
        do {
            let files = try fileManager.contentsOfDirectory(at: cacheDirectory, includingPropertiesForKeys: nil)
            return files.count
        } catch {
            return 0
        }
    }
}

// MARK: - AsyncSemaphore实现
actor AsyncSemaphore {
    private var value: Int
    private var waiters: [CheckedContinuation<Void, Never>] = []

    init(value: Int) {
        self.value = value
    }

    func wait() async {
        if value > 0 {
            value -= 1
        } else {
            await withCheckedContinuation { continuation in
                waiters.append(continuation)
            }
        }
    }

    func signal() {
        if waiters.isEmpty {
            value += 1
        } else {
            let waiter = waiters.removeFirst()
            waiter.resume()
        }
    }
}

// MARK: - 缓存统计信息
struct CacheStatistics {
    let memoryItems: Int
    let databaseItems: Int
    let fileSystemItems: Int

    var totalItems: Int {
        return memoryItems + databaseItems + fileSystemItems
    }
}
