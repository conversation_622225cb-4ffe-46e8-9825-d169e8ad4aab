//
//  FolderViewModel.swift
//  cop
//
//  Created by 阿亮 on 2025/6/1.
//

import Foundation
import SwiftUI
import Combine

// MARK: - 文件夹排序选项
enum FolderSortOption: String, CaseIterable {
    case name = "name"
    case dateModified = "dateModified"
    case mediaCount = "mediaCount"
    case size = "size"
    case fileCount = "fileCount"
    
    var displayName: String {
        switch self {
        case .name: return "名称"
        case .dateModified: return "修改时间"
        case .mediaCount: return "媒体数量"
        case .size: return "总大小"
        case .fileCount: return "文件数量"
        }
    }
}

// MARK: - 文件夹视图模式
enum FolderBrowseMode: String, CaseIterable {
    case grid = "grid"
    case list = "list"
    
    var displayName: String {
        switch self {
        case .grid: return "网格视图"
        case .list: return "列表视图"
        }
    }
    
    var icon: String {
        switch self {
        case .grid: return "square.grid.2x2"
        case .list: return "list.bullet"
        }
    }
}

// MARK: - 文件夹视图模型
@MainActor
class FolderViewModel: ObservableObject {
    
    // MARK: - Published Properties（文件夹级别）
    @Published var folders: [FolderInfo] = []
    @Published var filteredFolders: [FolderInfo] = []
    @Published var folderSearchText: String = ""
    @Published var folderSortOption: FolderSortOption = .dateModified
    @Published var folderSortDirection: SortDirection = .descending
    @Published var folderBrowseMode: FolderBrowseMode = .grid
    
    // MARK: - Published Properties（文件夹内容级别）
    @Published var selectedFolder: FolderInfo?
    @Published var folderMediaFiles: [MediaFileInfo] = []
    @Published var filteredFolderFiles: [MediaFileInfo] = []
    @Published var searchText: String = ""
    @Published var selectedMediaType: MediaType?
    @Published var sortOption: MediaSortOption = .dateCreated
    @Published var sortDirection: SortDirection = .descending
    @Published var viewMode: BrowseMode = .grid
    
    // MARK: - Published Properties（通用）
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var fullScreenMediaItem: FullScreenMediaItem?
    
    // MARK: - Private Properties
    private let mediaImportService = MediaImportService.shared
    private var cancellables = Set<AnyCancellable>()
    private let fileManager = FileManager.default

    // OPTIMIZED: 集成优化的缓存系统
    private let thumbnailCache = OptimizedThumbnailCache.shared
    
    // MARK: - 计算属性
    var totalFolderCount: Int {
        folders.count
    }
    
    var displayedFolders: [FolderInfo] {
        return filteredFolders
    }
    
    var displayedMediaFiles: [MediaFileInfo] {
        return filteredFolderFiles
    }
    
    // MARK: - 初始化
    init() {
        setupBindings()
        setupNotificationObservers()
        Task {
            await loadFolders()
        }
    }
    
    // MARK: - 设置数据绑定
    private func setupBindings() {
        // 文件夹搜索文本变化时过滤文件夹
        $folderSearchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .removeDuplicates()
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.filterFolders()
                }
            }
            .store(in: &cancellables)
        
        // 文件夹排序选项变化时重新排序
        Publishers.CombineLatest($folderSortOption, $folderSortDirection)
            .sink { [weak self] _, _ in
                Task { @MainActor in
                    self?.filterFolders()
                }
            }
            .store(in: &cancellables)
        
        // 媒体文件搜索文本变化时过滤媒体文件
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .removeDuplicates()
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.filterFolderFiles()
                }
            }
            .store(in: &cancellables)
        
        // 媒体类型筛选变化时重新过滤
        $selectedMediaType
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.filterFolderFiles()
                }
            }
            .store(in: &cancellables)
        
        // 媒体文件排序选项变化时重新排序
        Publishers.CombineLatest($sortOption, $sortDirection)
            .sink { [weak self] _, _ in
                Task { @MainActor in
                    self?.filterFolderFiles()
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - 设置通知监听
    private func setupNotificationObservers() {
        // 监听媒体导入完成通知
        NotificationCenter.default.publisher(for: .mediaImportCompleted)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.loadFolders()
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - 加载文件夹列表
    func loadFolders() async {
        isLoading = true
        errorMessage = nil

        do {
            folders = try await mediaImportService.getImportedFolders()
            filterFolders() // 加载完成后立即过滤
        } catch {
            errorMessage = "加载文件夹列表失败: \(error.localizedDescription)"
        }

        isLoading = false
    }
    
    // MARK: - 过滤文件夹
    private func filterFolders() {
        var filtered = folders
        
        // 按搜索文本过滤
        if !folderSearchText.isEmpty {
            filtered = filtered.filter { folder in
                folder.name.localizedCaseInsensitiveContains(folderSearchText)
            }
        }
        
        filteredFolders = filtered.sorted { sortFolders($0, $1) }
    }
    
    // MARK: - 排序文件夹
    private func sortFolders(_ lhs: FolderInfo, _ rhs: FolderInfo) -> Bool {
        let ascending = folderSortDirection == .ascending
        
        switch folderSortOption {
        case .name:
            return ascending ? lhs.name < rhs.name : lhs.name > rhs.name
        case .dateModified:
            return ascending ? lhs.lastModified < rhs.lastModified : lhs.lastModified > rhs.lastModified
        case .mediaCount:
            return ascending ? lhs.mediaCount < rhs.mediaCount : lhs.mediaCount > rhs.mediaCount
        case .size:
            return ascending ? lhs.totalSize < rhs.totalSize : lhs.totalSize > rhs.totalSize
        case .fileCount:
            return ascending ? lhs.fileCount < rhs.fileCount : lhs.fileCount > rhs.fileCount
        }
    }
    
    // MARK: - 选择文件夹
    func selectFolder(_ folder: FolderInfo?) {
        selectedFolder = folder
        if let folder = folder {
            print("=== FolderViewModel Debug ===")
            print("Selecting folder: \(folder.name)")
            print("Folder path: \(folder.path)")
            print("Folder exists: \(fileManager.fileExists(atPath: folder.path))")

            folderMediaFiles = getMediaFiles(in: folder)
            print("Found \(folderMediaFiles.count) media files")

            filterFolderFiles()
            print("After filtering: \(filteredFolderFiles.count) files")
            print("=== End FolderViewModel Debug ===")
        } else {
            folderMediaFiles = []
            filteredFolderFiles = []
        }
    }
    
    // MARK: - 获取文件夹中的媒体文件
    private func getMediaFiles(in folder: FolderInfo) -> [MediaFileInfo] {
        let folderURL = URL(fileURLWithPath: folder.path)
        var mediaFiles: [MediaFileInfo] = []

        print("  📂 Reading folder: \(folderURL.path)")

        do {
            let fileURLs = try fileManager.contentsOfDirectory(
                at: folderURL,
                includingPropertiesForKeys: [.isRegularFileKey],
                options: [.skipsHiddenFiles]
            ).filter { url in
                let resourceValues = try? url.resourceValues(forKeys: [.isRegularFileKey])
                return resourceValues?.isRegularFile == true
            }

            print("  📄 Found \(fileURLs.count) files in directory")

            for fileURL in fileURLs {
                print("    🔍 Processing: \(fileURL.lastPathComponent)")
                if let mediaFile = createMediaFileInfo(from: fileURL, folderPath: folder.name) {
                    print("      ✅ Added as \(mediaFile.type.rawValue): \(mediaFile.name)")
                    mediaFiles.append(mediaFile)
                } else {
                    print("      ❌ Skipped (not supported media type)")
                }
            }
        } catch {
            print("  ❌ 获取文件夹媒体文件失败: \(error)")
        }

        let sortedFiles = mediaFiles.sorted { sortMediaFiles($0, $1) }
        print("  📊 Returning \(sortedFiles.count) sorted media files")
        return sortedFiles
    }
    
    // MARK: - 创建媒体文件信息
    private func createMediaFileInfo(from url: URL, folderPath: String) -> MediaFileInfo? {
        guard let type = getMediaType(from: url) else { return nil }

        do {
            let resourceValues = try url.resourceValues(forKeys: [
                .fileSizeKey, .creationDateKey, .contentModificationDateKey
            ])

            let fileSize = Int64(resourceValues.fileSize ?? 0)
            let creationDate = resourceValues.creationDate ?? Date()
            let modificationDate = resourceValues.contentModificationDate ?? Date()

            // 使用文件路径生成稳定的UUID - 修复ID不一致问题
            let stableID = generateStableID(for: url)

            return MediaFileInfo(
                id: stableID,
                name: url.lastPathComponent,
                type: type,
                fileSize: fileSize,
                creationDate: creationDate,
                modificationDate: modificationDate,
                localURL: url,
                thumbnailURL: getThumbnailURL(for: url),
                folderPath: folderPath,
                duration: nil,
                dimensions: nil
            )
        } catch {
            return nil
        }
    }
    
    // MARK: - 生成稳定的文件ID
    private func generateStableID(for url: URL) -> UUID {
        // 使用文件路径的哈希值生成稳定的UUID
        let pathString = url.standardizedFileURL.path
        let hash = pathString.hashValue
        
        // 转换哈希值为字符串，然后生成UUID
        let _ = String(abs(hash))
        
        // 创建一个基于哈希值的稳定UUID
        // 使用MD5或类似方式，这里使用简化版本
        let uuidString = String(format: "%08X-%04X-%04X-%04X-%012X",
                               abs(hash) & 0xFFFFFFFF,
                               (abs(hash) >> 16) & 0xFFFF,
                               (abs(hash) >> 8) & 0xFFFF,
                               abs(hash) & 0xFFFF,
                               abs(hash) & 0xFFFFFFFFFFFF)
        
        return UUID(uuidString: uuidString) ?? UUID()
    }
    
    // MARK: - 获取媒体类型
    private func getMediaType(from url: URL) -> MediaType? {
        return MediaFormatDetector.detectMediaType(from: url)
    }
    
    // MARK: - 获取缩略图URL
    private func getThumbnailURL(for mediaURL: URL) -> URL? {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let thumbnailDirectory = documentsPath.appendingPathComponent("Thumbnails")
        
        let fileName = mediaURL.lastPathComponent
        let thumbnailFileName = "\(fileName.hashValue).jpg"
        let thumbnailURL = thumbnailDirectory.appendingPathComponent(thumbnailFileName)
        
        return fileManager.fileExists(atPath: thumbnailURL.path) ? thumbnailURL : nil
    }
    
    // MARK: - 过滤文件夹中的媒体文件
    private func filterFolderFiles() {
        var filtered = folderMediaFiles
        
        // 按搜索文本过滤
        if !searchText.isEmpty {
            filtered = filtered.filter { mediaFile in
                mediaFile.name.localizedCaseInsensitiveContains(searchText)
            }
        }
        
        // 按媒体类型过滤
        if let selectedMediaType = selectedMediaType {
            filtered = filtered.filter { $0.type == selectedMediaType }
        }
        
        filteredFolderFiles = filtered.sorted { sortMediaFiles($0, $1) }
    }
    
    // MARK: - 排序媒体文件
    private func sortMediaFiles(_ lhs: MediaFileInfo, _ rhs: MediaFileInfo) -> Bool {
        let ascending = sortDirection == .ascending
        
        switch sortOption {
        case .name:
            return ascending ? lhs.name < rhs.name : lhs.name > rhs.name
        case .dateCreated:
            return ascending ? lhs.creationDate < rhs.creationDate : lhs.creationDate > rhs.creationDate
        case .dateModified:
            return ascending ? lhs.modificationDate < rhs.modificationDate : lhs.modificationDate > rhs.modificationDate
        case .fileSize:
            return ascending ? lhs.fileSize < rhs.fileSize : lhs.fileSize > rhs.fileSize
        case .type:
            return ascending ? lhs.type.rawValue < rhs.type.rawValue : lhs.type.rawValue > rhs.type.rawValue
        }
    }
    
    // MARK: - 设置视图模式
    func setViewMode(_ mode: BrowseMode) {
        viewMode = mode
    }
    
    // MARK: - 设置文件夹视图模式
    func setFolderViewMode(_ mode: FolderBrowseMode) {
        folderBrowseMode = mode
    }
    
    // MARK: - 删除文件夹
    func deleteFolder(_ folder: FolderInfo) async {
        do {
            let folderURL = URL(fileURLWithPath: folder.path)
            try fileManager.removeItem(at: folderURL)
            
            // 重新加载文件夹列表
            await loadFolders()
            
            // 如果删除的是当前选中的文件夹，清除选择
            if selectedFolder?.name == folder.name {
                selectedFolder = nil
                folderMediaFiles = []
                filteredFolderFiles = []
            }
            
        } catch {
            errorMessage = "删除文件夹失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 删除媒体文件
    func deleteMediaFile(_ mediaFile: MediaFileInfo) async {
        do {
            // 删除原文件
            try fileManager.removeItem(at: mediaFile.localURL)
            
            // 删除缩略图
            if let thumbnailURL = mediaFile.thumbnailURL {
                try? fileManager.removeItem(at: thumbnailURL)
            }
            
            // 重新加载当前文件夹内容
            if let selectedFolder = selectedFolder {
                selectFolder(selectedFolder)
            }
            
        } catch {
            errorMessage = "删除文件失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 清除错误消息
    func clearError() {
        errorMessage = nil
    }
    
    // MARK: - 设置文件夹排序选项
    func setSortOption(_ option: FolderSortOption) {
        folderSortOption = option
    }
    
    // MARK: - 设置文件夹排序方向
    func setSortDirection(_ direction: SortDirection) {
        folderSortDirection = direction
    }
    
    // MARK: - 取消选择文件夹
    func deselectFolder() {
        selectedFolder = nil
        folderMediaFiles = []
        filteredFolderFiles = []
    }
    
    // MARK: - 导入文件夹
    func importFolder(from url: URL) async {
        do {
            let importedFiles = try await mediaImportService.importMediaFolder(from: url)
            
            // 重新加载文件夹列表
            await loadFolders()
            
            // 显示成功消息
            errorMessage = "成功导入 \(importedFiles.count) 个文件"
            
        } catch {
            errorMessage = "导入失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 重新扫描文件夹
    func rescanFolder(_ folder: FolderInfo) async {
        // 重新加载文件夹内容
        if selectedFolder?.name == folder.name {
            selectFolder(folder)
        }
        
        // 重新加载文件夹列表
        await loadFolders()
    }
    
    // MARK: - 选择媒体文件
    func selectMediaFile(_ mediaFile: MediaFileInfo) {
        // 这里可以实现媒体文件选择逻辑
        // 例如显示详情页面或全屏预览
    }

    // MARK: - 全屏查看器
    func showFullScreenViewer(mediaFiles: [MediaFileInfo], at index: Int, initialUIState: UIVisibilityState) {
        fullScreenMediaItem = FullScreenMediaItem(
            mediaFiles: mediaFiles,
            initialIndex: index,
            initialUIState: initialUIState
        )
    }
}