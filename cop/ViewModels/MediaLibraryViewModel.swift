//
//  MediaLibraryViewModel.swift
//  cop
//
//  Created by 阿亮 on 2025/5/31.
//

import Foundation
import SwiftUI
import Combine
import CryptoKit

// MARK: - 内容模式枚举
enum ContentMode: String, CaseIterable {
    case folder = "folder"
    case all = "all"
    
    var displayName: String {
        switch self {
        case .folder: return "按文件夹"
        case .all: return "全部内容"
        }
    }
    
    var icon: String {
        switch self {
        case .folder: return "folder"
        case .all: return "square.grid.3x3"
        }
    }
}

// MARK: - 浏览模式枚举
enum BrowseMode: String, CaseIterable {
    case grid = "grid"
    case list = "list"
    case waterfall = "waterfall"
    
    var displayName: String {
        switch self {
        case .grid: return "网格视图"
        case .list: return "列表视图"
        case .waterfall: return "瀑布视图"
        }
    }
    
    var icon: String {
        switch self {
        case .grid: return "square.grid.2x2"
        case .list: return "list.bullet"
        case .waterfall: return "rectangle.grid.1x2"
        }
    }
}

// MARK: - 媒体库视图模型
@MainActor
class MediaLibraryViewModel: ObservableObject {
    
    static let shared = MediaLibraryViewModel()
    
    // MARK: - Published Properties
    @Published var allMediaFiles: [MediaFileInfo] = []
    @Published var filteredMediaFiles: [MediaFileInfo] = []
    @Published var folders: [FolderInfo] = []
    @Published var viewMode: BrowseMode = .grid
    @Published var searchText: String = ""
    @Published var selectedMediaType: MediaType?
    @Published var sortOption: MediaSortOption = .dateCreated
    @Published var sortDirection: SortDirection = .descending
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var showingImportSheet = false
    @Published var showingSettings = false
    @Published var fullScreenMediaItem: FullScreenMediaItem?
    
    // MARK: - Private Properties
    private let mediaImportService = MediaImportService.shared
    private var cancellables = Set<AnyCancellable>()
    private let fileManager = FileManager.default

    // OPTIMIZED: 集成优化的缓存系统
    private let thumbnailCache = OptimizedThumbnailCache.shared
    
    // MARK: - 计算属性
    var totalMediaCount: Int {
        allMediaFiles.count
    }
    
    var displayedMediaFiles: [MediaFileInfo] {
        return filteredMediaFiles
    }
    
    // MARK: - 初始化
    init() {
        setupBindings()
        setupNotificationObservers()
        Task {
            await loadMediaLibrary()
        }
    }
    
    // MARK: - 设置数据绑定
    private func setupBindings() {
        // 搜索文本变化时过滤媒体文件
        $searchText
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .removeDuplicates()
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.filterMediaFiles()
                }
            }
            .store(in: &cancellables)
        
        // 媒体类型筛选变化时重新过滤
        $selectedMediaType
            .sink { [weak self] _ in
                Task { @MainActor in
                    self?.filterMediaFiles()
                }
            }
            .store(in: &cancellables)
        
        // 排序选项变化时重新排序
        Publishers.CombineLatest($sortOption, $sortDirection)
            .sink { [weak self] _, _ in
                Task { @MainActor in
                    self?.filterMediaFiles() // 重新过滤并排序
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - 设置通知监听
    private func setupNotificationObservers() {
        // 监听媒体导入完成通知
        NotificationCenter.default.publisher(for: .mediaImportCompleted)
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                Task { @MainActor in
                    await self?.loadMediaLibrary()
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - 加载媒体库
    func loadMediaLibrary() async {
        isLoading = true
        errorMessage = nil
        
        // 加载所有媒体文件
        await loadAllMediaFiles()
        
        isLoading = false
    }
    
    // MARK: - 加载所有媒体文件
    private func loadAllMediaFiles() async {
        do {
            // 直接从导入服务获取所有媒体文件
            let importedFolders = try await mediaImportService.getImportedFolders()
            folders = importedFolders
            var allFiles: [MediaFileInfo] = []
            
            for folder in importedFolders {
                let folderMediaFiles = getMediaFiles(in: folder)
                allFiles.append(contentsOf: folderMediaFiles)
            }
            
            allMediaFiles = allFiles
            filterMediaFiles()
        } catch {
            errorMessage = "加载媒体文件失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 获取文件夹中的媒体文件
    private func getMediaFiles(in folder: FolderInfo) -> [MediaFileInfo] {
        let folderURL = URL(fileURLWithPath: folder.path)
        var mediaFiles: [MediaFileInfo] = []
        
        do {
            let fileURLs = try fileManager.contentsOfDirectory(
                at: folderURL,
                includingPropertiesForKeys: [.isRegularFileKey],
                options: [.skipsHiddenFiles]
            ).filter { url in
                let resourceValues = try? url.resourceValues(forKeys: [.isRegularFileKey])
                return resourceValues?.isRegularFile == true
            }
            
            for fileURL in fileURLs {
                if let mediaFile = createMediaFileInfo(from: fileURL, folderPath: folder.name) {
                    mediaFiles.append(mediaFile)
                }
            }
        } catch {
            print("获取文件夹媒体文件失败: \(error)")
        }
        
        return mediaFiles.sorted { sortMediaFiles($0, $1) }
    }
    
    // MARK: - 创建媒体文件信息
    private func createMediaFileInfo(from url: URL, folderPath: String) -> MediaFileInfo? {
        // 这里应该从持久化存储中读取，暂时简化实现
        guard let type = getMediaType(from: url) else { return nil }
        
        do {
            let resourceValues = try url.resourceValues(forKeys: [
                .fileSizeKey, .creationDateKey, .contentModificationDateKey
            ])
            
            let fileSize = Int64(resourceValues.fileSize ?? 0)
            let creationDate = resourceValues.creationDate ?? Date()
            let modificationDate = resourceValues.contentModificationDate ?? Date()
            
            // 使用文件路径生成稳定的UUID - 修复ID不一致问题
            let stableID = generateStableID(for: url)
            
            return MediaFileInfo(
                id: stableID,
                name: url.lastPathComponent,
                type: type,
                fileSize: fileSize,
                creationDate: creationDate,
                modificationDate: modificationDate,
                localURL: url,
                thumbnailURL: getThumbnailURL(for: url),
                folderPath: folderPath,
                duration: nil,
                dimensions: nil
            )
        } catch {
            return nil
        }
    }
    
    // MARK: - 生成稳定的文件ID
    private func generateStableID(for url: URL) -> UUID {
        // 使用文件路径的MD5哈希生成稳定的UUID
        let pathString = url.standardizedFileURL.path
        
        // 创建MD5哈希
        let data = Data(pathString.utf8)
        let hash = Insecure.MD5.hash(data: data)
        let hashString = hash.compactMap { String(format: "%02x", $0) }.joined()
        
        // 从MD5哈希创建UUID格式的字符串
        let uuidString = String(format: "%@-%@-%@-%@-%@",
                               String(hashString.prefix(8)),
                               String(hashString.dropFirst(8).prefix(4)),
                               String(hashString.dropFirst(12).prefix(4)),
                               String(hashString.dropFirst(16).prefix(4)),
                               String(hashString.dropFirst(20).prefix(12)))
        
        return UUID(uuidString: uuidString) ?? UUID()
    }
    
    // MARK: - 获取媒体类型
    private func getMediaType(from url: URL) -> MediaType? {
        let pathExtension = url.pathExtension.lowercased()
        
        let imageExtensions = ["jpg", "jpeg", "png", "heic", "heif", "gif", "bmp", "tiff"]
        let videoExtensions = ["mov", "mp4", "avi", "mkv", "m4v", "3gp"]
        
        if imageExtensions.contains(pathExtension) {
            return .image
        } else if videoExtensions.contains(pathExtension) {
            return .video
        }
        
        return nil
    }
    
    // MARK: - 获取缩略图URL
    private func getThumbnailURL(for mediaURL: URL) -> URL? {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let thumbnailDirectory = documentsPath.appendingPathComponent("Thumbnails")
        
        // 基于文件路径生成缩略图文件名
        let fileName = mediaURL.lastPathComponent
        let thumbnailFileName = "\(fileName.hashValue).jpg"
        let thumbnailURL = thumbnailDirectory.appendingPathComponent(thumbnailFileName)
        
        return fileManager.fileExists(atPath: thumbnailURL.path) ? thumbnailURL : nil
    }
    
    // MARK: - 过滤媒体文件 (OPTIMIZED)
    private func filterMediaFiles() {
        var filtered = allMediaFiles

        // 按搜索文本过滤
        if !searchText.isEmpty {
            filtered = filtered.filter { mediaFile in
                mediaFile.name.localizedCaseInsensitiveContains(searchText) ||
                mediaFile.folderPath.localizedCaseInsensitiveContains(searchText)
            }
        }

        // 按媒体类型过滤
        if let selectedMediaType = selectedMediaType {
            filtered = filtered.filter { $0.type == selectedMediaType }
        }

        filteredMediaFiles = filtered.sorted { sortMediaFiles($0, $1) }

        // OPTIMIZED: 预加载前50个项目的缩略图
        Task {
            let preloadItems = Array(filteredMediaFiles.prefix(50))
            let mediaFileIDs = preloadItems.map { $0.id }
            await thumbnailCache.preloadThumbnails(for: mediaFileIDs)
        }
    }
    
    // MARK: - 排序媒体文件
    private func sortMediaFiles() {
        filteredMediaFiles.sort { sortMediaFiles($0, $1) }
    }
    
    private func sortMediaFiles(_ lhs: MediaFileInfo, _ rhs: MediaFileInfo) -> Bool {
        let ascending = sortDirection == .ascending
        
        switch sortOption {
        case .name:
            return ascending ? lhs.name < rhs.name : lhs.name > rhs.name
        case .dateCreated:
            return ascending ? lhs.creationDate < rhs.creationDate : lhs.creationDate > rhs.creationDate
        case .dateModified:
            return ascending ? lhs.modificationDate < rhs.modificationDate : lhs.modificationDate > rhs.modificationDate
        case .fileSize:
            return ascending ? lhs.fileSize < rhs.fileSize : lhs.fileSize > rhs.fileSize
        case .type:
            return ascending ? lhs.type.rawValue < rhs.type.rawValue : lhs.type.rawValue > rhs.type.rawValue
        }
    }
    
    // MARK: - 导入媒体文件夹
    func importMediaFolder(from url: URL) async {
        do {
            let importedFiles = try await mediaImportService.importMediaFolder(from: url)
            
            // 重新加载媒体库
            await loadMediaLibrary()
            
            // 显示成功消息
            errorMessage = "成功导入 \(importedFiles.count) 个文件"
            
        } catch {
            errorMessage = "导入失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 切换视图模式
    func setViewMode(_ mode: BrowseMode) {
        viewMode = mode
    }
    
    // MARK: - 删除媒体文件
    func deleteMediaFile(_ mediaFile: MediaFileInfo) async {
        do {
            // 删除原文件
            try fileManager.removeItem(at: mediaFile.localURL)
            
            // 删除缩略图
            if let thumbnailURL = mediaFile.thumbnailURL {
                try? fileManager.removeItem(at: thumbnailURL)
            }
            
            // 重新加载媒体库
            await loadMediaLibrary()
            
        } catch {
            errorMessage = "删除文件失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 清除错误消息
    func clearError() {
        errorMessage = nil
    }
    
    // MARK: - 获取存储使用情况
    func getStorageInfo() -> (usedSpace: Int64, totalSpace: Int64) {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        
        do {
            let resourceValues = try documentsPath.resourceValues(forKeys: [.volumeAvailableCapacityKey, .volumeTotalCapacityKey])
            let totalSpace = Int64(resourceValues.volumeTotalCapacity ?? 0)
            let availableSpace = Int64(resourceValues.volumeAvailableCapacity ?? 0)
            let usedSpace = totalSpace - availableSpace
            
            return (usedSpace: usedSpace, totalSpace: totalSpace)
        } catch {
            return (usedSpace: 0, totalSpace: 0)
        }
    }
    
    // MARK: - 清空所有数据
    func clearAllData() async {
        do {
            let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
            let mediaPath = documentsPath.appendingPathComponent("Media")
            let thumbnailPath = documentsPath.appendingPathComponent("Thumbnails")
            
            // 删除媒体文件夹
            if fileManager.fileExists(atPath: mediaPath.path) {
                try fileManager.removeItem(at: mediaPath)
            }
            
            // 删除缩略图文件夹
            if fileManager.fileExists(atPath: thumbnailPath.path) {
                try fileManager.removeItem(at: thumbnailPath)
            }
            
            // 重新创建目录
            try fileManager.createDirectory(at: mediaPath, withIntermediateDirectories: true)
            try fileManager.createDirectory(at: thumbnailPath, withIntermediateDirectories: true)
            
            // 重新加载媒体库
            await loadMediaLibrary()
            
        } catch {
            errorMessage = "清空数据失败: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 清理缩略图缓存 (OPTIMIZED)
    func clearThumbnailCache() async {
        // OPTIMIZED: 使用优化缓存系统清理所有缓存层
        await thumbnailCache.clearAllCaches()

        // 重新加载媒体库
        await loadMediaLibrary()
    }
    
    // MARK: - 重新生成缩略图
    func regenerateThumbnails() async {
        // 清理现有缩略图
        await clearThumbnailCache()

        // 重新加载媒体库，这将触发缩略图的重新生成
        await loadMediaLibrary()
    }
    
    // MARK: - 刷新所有媒体
    func refreshAllMedia() async {
        await loadMediaLibrary()
    }

    // MARK: - 全屏查看器
    func showFullScreenViewer(mediaFiles: [MediaFileInfo], at index: Int, initialUIState: UIVisibilityState) {
        fullScreenMediaItem = FullScreenMediaItem(
            mediaFiles: mediaFiles,
            initialIndex: index,
            initialUIState: initialUIState
        )
    }
}

// MARK: - 全屏媒体项目
struct FullScreenMediaItem: Identifiable {
    let id = UUID()
    let mediaFiles: [MediaFileInfo]
    let initialIndex: Int
    let initialUIState: UIVisibilityState
}