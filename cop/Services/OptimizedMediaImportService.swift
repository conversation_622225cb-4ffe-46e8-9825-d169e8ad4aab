//
//  OptimizedMediaImportService.swift
//  cop
//
//  Created by Augment Agent on 2025-06-18.
//  OPTIMIZED: 高性能媒体导入服务，支持海量文件处理
//

import Foundation
import SwiftUI
import UIKit
import AVFoundation
import SQLite3
import os.log

// MARK: - 优化的媒体导入服务
@MainActor
class OptimizedMediaImportService: ObservableObject {
    static let shared = OptimizedMediaImportService()
    
    @Published var isImporting = false
    @Published var importProgress = ImportProgress()
    
    private let fileManager = FileManager.default
    private let logger = Logger(subsystem: "com.cop.app", category: "OptimizedMediaImport")
    
    // OPTIMIZED: 并发处理队列
    private let importQueue = DispatchQueue(
        label: "media.import.concurrent",
        qos: .userInitiated,
        attributes: .concurrent
    )
    
    // OPTIMIZED: 缩略图生成队列
    private let thumbnailQueue = DispatchQueue(
        label: "thumbnail.generation",
        qos: .utility,
        attributes: .concurrent
    )
    
    // OPTIMIZED: 批处理配置
    private let batchSize = 20 // 每批处理20个文件
    private let maxConcurrentOperations = 4 // 最大并发数
    
    // OPTIMIZED: 内存管理
    private let memoryThreshold: UInt64 = 200 * 1024 * 1024 // 200MB
    
    private var importTask: Task<Void, Never>?
    private let thumbnailCache = OptimizedThumbnailCache.shared
    private let databaseManager = MediaDatabaseManager.shared
    
    private init() {
        setupDirectories()
        setupMemoryMonitoring()
    }
    
    // MARK: - OPTIMIZED: 高性能批量导入
    func importMediaFolder(from sourceURL: URL) async throws -> [MediaFileInfo] {
        logger.info("🚀 开始优化导入: \(sourceURL.path)")
        
        isImporting = true
        importProgress.startTime = Date()
        
        defer {
            isImporting = false
            importProgress.reset()
            importTask = nil
        }
        
        // OPTIMIZED: 安全访问权限管理
        guard sourceURL.startAccessingSecurityScopedResource() else {
            throw MediaImportError.accessDenied
        }
        defer { sourceURL.stopAccessingSecurityScopedResource() }
        
        // OPTIMIZED: 并发扫描媒体文件
        let mediaFiles = try await scanMediaFilesConcurrently(in: sourceURL)
        logger.info("📊 扫描到 \(mediaFiles.count) 个媒体文件")
        
        importProgress.totalCount = mediaFiles.count
        
        // OPTIMIZED: 分批并发处理
        var importedFiles: [MediaFileInfo] = []
        let batches = mediaFiles.chunked(into: batchSize)
        
        for (batchIndex, batch) in batches.enumerated() {
            logger.info("📦 处理批次 \(batchIndex + 1)/\(batches.count)")
            
            // 检查内存使用情况
            if getCurrentMemoryUsage() > memoryThreshold {
                await performMemoryCleanup()
            }
            
            let batchResults = await processBatchConcurrently(batch)
            importedFiles.append(contentsOf: batchResults)
            
            // 更新进度
            importProgress.processedCount = importedFiles.count
            importProgress.progress = Double(importedFiles.count) / Double(mediaFiles.count)
            
            // OPTIMIZED: 批量写入数据库
            try await databaseManager.insertMediaFiles(batchResults)
        }
        
        // OPTIMIZED: 后台生成缩略图
        Task.detached(priority: .utility) {
            await self.generateThumbnailsInBackground(for: importedFiles)
        }
        
        logger.info("✅ 导入完成: \(importedFiles.count) 个文件")
        NotificationCenter.default.post(name: .mediaImportCompleted, object: nil)
        
        return importedFiles
    }
    
    // MARK: - OPTIMIZED: 并发扫描
    private func scanMediaFilesConcurrently(in directory: URL) async throws -> [URL] {
        return try await withTaskGroup(of: [URL].self) { group in
            var allMediaFiles: [URL] = []
            
            // 获取顶级目录
            let topLevelURLs = try fileManager.contentsOfDirectory(
                at: directory,
                includingPropertiesForKeys: [.isDirectoryKey],
                options: [.skipsHiddenFiles]
            )
            
            // 为每个顶级目录创建并发任务
            for url in topLevelURLs {
                group.addTask {
                    try await self.scanDirectoryRecursively(url)
                }
            }
            
            // 收集所有结果
            for await mediaFiles in group {
                allMediaFiles.append(contentsOf: mediaFiles)
            }
            
            return allMediaFiles
        }
    }
    
    // MARK: - OPTIMIZED: 递归扫描单个目录
    private func scanDirectoryRecursively(_ directory: URL) async throws -> [URL] {
        var mediaFiles: [URL] = []
        let resourceKeys: [URLResourceKey] = [.isRegularFileKey, .isDirectoryKey]
        
        let fileURLs = try fileManager.contentsOfDirectory(
            at: directory,
            includingPropertiesForKeys: resourceKeys,
            options: [.skipsHiddenFiles]
        )
        
        for fileURL in fileURLs {
            let resourceValues = try fileURL.resourceValues(forKeys: Set(resourceKeys))
            
            if resourceValues.isDirectory == true {
                // 递归扫描子目录
                let subMediaFiles = try await scanDirectoryRecursively(fileURL)
                mediaFiles.append(contentsOf: subMediaFiles)
            } else if resourceValues.isRegularFile == true,
                      MediaFormatDetector.isSupportedMediaFile(fileURL) {
                mediaFiles.append(fileURL)
            }
        }
        
        return mediaFiles
    }
    
    // MARK: - OPTIMIZED: 并发批处理
    private func processBatchConcurrently(_ batch: [URL]) async -> [MediaFileInfo] {
        return await withTaskGroup(of: MediaFileInfo?.self) { group in
            var results: [MediaFileInfo] = []
            
            // 限制并发数量
            let semaphore = DispatchSemaphore(value: maxConcurrentOperations)
            
            for fileURL in batch {
                group.addTask {
                    semaphore.wait()
                    defer { semaphore.signal() }
                    
                    do {
                        return try await self.importSingleFileOptimized(fileURL)
                    } catch {
                        self.logger.error("❌ 导入失败: \(fileURL.lastPathComponent) - \(error)")
                        return nil
                    }
                }
            }
            
            // 收集结果
            for await result in group {
                if let mediaFile = result {
                    results.append(mediaFile)
                }
            }
            
            return results
        }
    }
    
    // MARK: - OPTIMIZED: 单文件导入优化
    private func importSingleFileOptimized(_ sourceURL: URL) async throws -> MediaFileInfo {
        guard let mediaType = MediaFormatDetector.detectMediaType(from: sourceURL) else {
            throw MediaImportError.unsupportedFileType
        }
        
        let fileName = sourceURL.lastPathComponent
        let folderName = sourceURL.deletingLastPathComponent().lastPathComponent
        
        // OPTIMIZED: 目标路径计算
        let targetFolderURL = getMediaDirectory().appendingPathComponent(folderName)
        try fileManager.createDirectory(at: targetFolderURL, withIntermediateDirectories: true)
        
        let finalTargetURL = try generateUniqueTargetURL(
            fileName: fileName,
            targetFolder: targetFolderURL
        )
        
        // OPTIMIZED: 流式文件复制（大文件优化）
        try await copyFileStreaming(from: sourceURL, to: finalTargetURL)
        
        // 生成稳定ID
        let fileID = generateStableID(for: finalTargetURL)
        
        // 获取文件元数据
        let metadata = try await extractFileMetadata(from: finalTargetURL, mediaType: mediaType)
        
        return MediaFileInfo(
            id: fileID,
            name: fileName,
            type: mediaType,
            fileSize: metadata.fileSize,
            creationDate: metadata.creationDate,
            modificationDate: metadata.modificationDate,
            localURL: finalTargetURL,
            thumbnailURL: nil, // 后台生成
            folderPath: folderName,
            duration: metadata.duration,
            dimensions: metadata.dimensions
        )
    }
    
    // MARK: - OPTIMIZED: 流式文件复制
    private func copyFileStreaming(from sourceURL: URL, to targetURL: URL) async throws {
        let bufferSize = 64 * 1024 // 64KB buffer
        
        guard let inputStream = InputStream(url: sourceURL),
              let outputStream = OutputStream(url: targetURL, append: false) else {
            throw MediaImportError.copyFailed
        }
        
        inputStream.open()
        outputStream.open()
        
        defer {
            inputStream.close()
            outputStream.close()
        }
        
        let buffer = UnsafeMutablePointer<UInt8>.allocate(capacity: bufferSize)
        defer { buffer.deallocate() }
        
        while inputStream.hasBytesAvailable {
            let bytesRead = inputStream.read(buffer, maxLength: bufferSize)
            if bytesRead > 0 {
                let bytesWritten = outputStream.write(buffer, maxLength: bytesRead)
                if bytesWritten != bytesRead {
                    throw MediaImportError.copyFailed
                }
            }
            
            // 让出CPU时间，避免阻塞
            await Task.yield()
        }
    }
    
    // MARK: - OPTIMIZED: 后台缩略图生成
    private func generateThumbnailsInBackground(for mediaFiles: [MediaFileInfo]) async {
        logger.info("🖼️ 开始后台生成 \(mediaFiles.count) 个缩略图")
        
        await withTaskGroup(of: Void.self) { group in
            let semaphore = DispatchSemaphore(value: 2) // 限制并发数
            
            for mediaFile in mediaFiles {
                group.addTask {
                    semaphore.wait()
                    defer { semaphore.signal() }
                    
                    do {
                        let thumbnail = try await self.generateOptimizedThumbnail(for: mediaFile)
                        await self.thumbnailCache.storeThumbnail(thumbnail, for: mediaFile.id)
                    } catch {
                        self.logger.error("🖼️ 缩略图生成失败: \(mediaFile.name)")
                    }
                }
            }
        }
        
        logger.info("✅ 缩略图生成完成")
    }
    
    // MARK: - OPTIMIZED: 优化的缩略图生成
    private func generateOptimizedThumbnail(for mediaFile: MediaFileInfo) async throws -> UIImage {
        let thumbnailSize = CGSize(width: 300, height: 300)
        
        switch mediaFile.type {
        case .image:
            return try await generateImageThumbnailOptimized(
                from: mediaFile.localURL,
                size: thumbnailSize
            )
        case .video:
            return try await generateVideoThumbnailOptimized(
                from: mediaFile.localURL,
                size: thumbnailSize
            )
        }
    }
    
    // MARK: - OPTIMIZED: 图片缩略图优化生成
    private func generateImageThumbnailOptimized(from sourceURL: URL, size: CGSize) async throws -> UIImage {
        // OPTIMIZED: 使用ImageIO进行内存高效的缩略图生成
        guard let imageSource = CGImageSourceCreateWithURL(sourceURL as CFURL, nil) else {
            throw MediaImportError.thumbnailGenerationFailed
        }
        
        let options: [CFString: Any] = [
            kCGImageSourceCreateThumbnailFromImageIfAbsent: true,
            kCGImageSourceCreateThumbnailWithTransform: true,
            kCGImageSourceThumbnailMaxPixelSize: max(size.width, size.height),
            kCGImageSourceShouldCache: false // 避免额外内存占用
        ]
        
        guard let thumbnail = CGImageSourceCreateThumbnailAtIndex(imageSource, 0, options as CFDictionary) else {
            throw MediaImportError.thumbnailGenerationFailed
        }
        
        return UIImage(cgImage: thumbnail)
    }
    
    // MARK: - OPTIMIZED: 视频缩略图优化生成
    private func generateVideoThumbnailOptimized(from sourceURL: URL, size: CGSize) async throws -> UIImage {
        let asset = AVURLAsset(url: sourceURL)
        
        // OPTIMIZED: 检查资源可用性
        let isPlayable = try await asset.load(.isPlayable)
        guard isPlayable else {
            return try generateDefaultVideoThumbnail(size: size)
        }
        
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        imageGenerator.maximumSize = size
        imageGenerator.requestedTimeToleranceBefore = .zero
        imageGenerator.requestedTimeToleranceAfter = .zero
        
        // OPTIMIZED: 智能时间点选择
        let duration = try await asset.load(.duration)
        let thumbnailTime = CMTime(seconds: min(3.0, CMTimeGetSeconds(duration) * 0.1), preferredTimescale: 600)
        
        do {
            let cgImage = try await imageGenerator.image(at: thumbnailTime).image
            return UIImage(cgImage: cgImage)
        } catch {
            return try generateDefaultVideoThumbnail(size: size)
        }
    }
    
    // MARK: - Helper Methods
    
    private func setupDirectories() {
        try? fileManager.createDirectory(at: getMediaDirectory(), withIntermediateDirectories: true)
        try? fileManager.createDirectory(at: getThumbnailDirectory(), withIntermediateDirectories: true)
    }
    
    private func setupMemoryMonitoring() {
        // 监听内存警告
        NotificationCenter.default.addObserver(
            forName: UIApplication.didReceiveMemoryWarningNotification,
            object: nil,
            queue: .main
        ) { _ in
            Task { await self.performMemoryCleanup() }
        }
    }
    
    private func performMemoryCleanup() async {
        logger.info("🧹 执行内存清理")
        await thumbnailCache.clearMemoryCache()
        // 触发垃圾回收
        autoreleasepool { }
    }
    
    private func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        return kerr == KERN_SUCCESS ? info.resident_size : 0
    }
    
    private func getMediaDirectory() -> URL {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        return documentsPath.appendingPathComponent("MediaFiles")
    }
    
    private func getThumbnailDirectory() -> URL {
        let documentsPath = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]
        return documentsPath.appendingPathComponent("Thumbnails")
    }

    private func extractFileMetadata(from fileURL: URL, mediaType: MediaType) async throws -> FileMetadata {
        let resourceValues = try fileURL.resourceValues(forKeys: [
            .fileSizeKey, .creationDateKey, .contentModificationDateKey
        ])

        let fileSize = Int64(resourceValues.fileSize ?? 0)
        let creationDate = resourceValues.creationDate ?? Date()
        let modificationDate = resourceValues.contentModificationDate ?? Date()

        var duration: TimeInterval?
        var dimensions: CGSize?

        switch mediaType {
        case .video:
            let asset = AVURLAsset(url: fileURL)
            do {
                duration = try await asset.load(.duration).seconds
                if let track = try await asset.loadTracks(withMediaType: .video).first {
                    dimensions = try await track.load(.naturalSize)
                }
            } catch {
                logger.error("获取视频元数据失败: \(error)")
            }
        case .image:
            if let imageSource = CGImageSourceCreateWithURL(fileURL as CFURL, nil),
               let properties = CGImageSourceCopyPropertiesAtIndex(imageSource, 0, nil) as? [CFString: Any] {
                let width = properties[kCGImagePropertyPixelWidth] as? CGFloat ?? 0
                let height = properties[kCGImagePropertyPixelHeight] as? CGFloat ?? 0
                dimensions = CGSize(width: width, height: height)
            }
        }

        return FileMetadata(
            fileSize: fileSize,
            creationDate: creationDate,
            modificationDate: modificationDate,
            duration: duration,
            dimensions: dimensions
        )
    }

    private func generateUniqueTargetURL(fileName: String, targetFolder: URL) throws -> URL {
        let targetFileURL = targetFolder.appendingPathComponent(fileName)

        if !fileManager.fileExists(atPath: targetFileURL.path) {
            return targetFileURL
        }

        // 生成唯一文件名
        let timestamp = Int(Date().timeIntervalSince1970)
        let nameWithoutExtension = targetFileURL.deletingPathExtension().lastPathComponent
        let fileExtension = targetFileURL.pathExtension
        let newFileName = "\(nameWithoutExtension)_\(timestamp).\(fileExtension)"

        return targetFolder.appendingPathComponent(newFileName)
    }

    private func generateStableID(for url: URL) -> UUID {
        let pathString = url.standardizedFileURL.path
        let hash = pathString.hashValue

        let uuidString = String(format: "%08X-%04X-%04X-%04X-%012X",
                               abs(hash) & 0xFFFFFFFF,
                               (abs(hash) >> 16) & 0xFFFF,
                               (abs(hash) >> 8) & 0xFFFF,
                               abs(hash) & 0xFFFF,
                               abs(hash) & 0xFFFFFFFFFFFF)

        return UUID(uuidString: uuidString) ?? UUID()
    }

    private func generateDefaultVideoThumbnail(size: CGSize) throws -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            UIColor.systemGray5.setFill()
            context.fill(CGRect(origin: .zero, size: size))

            let iconSize: CGFloat = min(size.width, size.height) * 0.4
            let iconRect = CGRect(
                x: (size.width - iconSize) / 2,
                y: (size.height - iconSize) / 2,
                width: iconSize,
                height: iconSize
            )

            UIColor.systemGray.setFill()
            let path = UIBezierPath(roundedRect: iconRect, cornerRadius: iconSize * 0.1)
            path.fill()
        }
    }
}

// MARK: - 扩展错误类型
extension MediaImportError {
    static let copyFailed = MediaImportError.insufficientStorage // 复用现有错误类型
}

// MARK: - 文件元数据结构
struct FileMetadata {
    let fileSize: Int64
    let creationDate: Date
    let modificationDate: Date
    let duration: TimeInterval?
    let dimensions: CGSize?
}

// MARK: - Array扩展：分块处理
extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}
