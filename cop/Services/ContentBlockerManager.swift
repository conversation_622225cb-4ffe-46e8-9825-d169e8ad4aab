//
//  ContentBlockerManager.swift
//  cop
//
//  Created by Augment Agent on 2025/6/15.
//

import Foundation
import WebKit
import OSLog
import Network

// MARK: - 过滤列表类型
enum FilterListType: String, CaseIterable {
    case easyList = "easylist"
    case easyPrivacy = "easyprivacy"
    case fanboyAnnoyances = "fanboy-annoyances"
    case easyListChina = "easylist-china"
    
    var displayName: String {
        switch self {
        case .easyList:
            return "EasyList（基础广告屏蔽）"
        case .easyPrivacy:
            return "EasyPrivacy（隐私保护）"
        case .fanboyAnnoyances:
            return "Fanboy's Annoyances（干扰元素）"
        case .easyListChina:
            return "EasyList China（中国地区）"
        }
    }
    
    var downloadURL: URL? {
        switch self {
        case .easyList:
            // 使用更可靠的主URL和备选URL
            return URL(string: "https://easylist.to/easylist/easylist.txt")
        case .easyPrivacy:
            return URL(string: "https://easylist.to/easylist/easyprivacy.txt")
        case .fanboyAnnoyances:
            return URL(string: "https://easylist.to/easylist/fanboy-annoyance.txt")
        case .easyListChina:
            return URL(string: "https://easylist-downloads.adblockplus.org/easylistchina.txt")
        }
    }
    
    // 备选下载URL
    var alternativeDownloadURLs: [URL] {
        switch self {
        case .easyList:
            return [
                URL(string: "https://v.firebog.net/hosts/Easylist.txt"),
                URL(string: "https://filters.adtidy.org/extension/ublock/filters/2_without_easylist.txt"),
                URL(string: "https://raw.githubusercontent.com/easylist/easylist/master/easylist/easylist_general_block.txt")
            ].compactMap { $0 }
        case .easyPrivacy:
            return [
                URL(string: "https://v.firebog.net/hosts/Easyprivacy.txt"),
                URL(string: "https://filters.adtidy.org/extension/ublock/filters/3.txt")
            ].compactMap { $0 }
        case .fanboyAnnoyances:
            return [
                URL(string: "https://filters.adtidy.org/extension/ublock/filters/14.txt")
            ].compactMap { $0 }
        case .easyListChina:
            return [
                URL(string: "https://filters.adtidy.org/extension/ublock/filters/224.txt")
            ].compactMap { $0 }
        }
    }
    
    var maxRules: Int {
        // iOS 18支持每个WKContentRuleList最多150,000条规则
        // 我们为每个列表分配更大的规则数量以获得更好的屏蔽效果
        switch self {
        case .easyList:
            return 120000  // 基础广告屏蔽 - 最重要，分配最多规则
        case .easyPrivacy:
            return 80000   // 隐私保护 - 重要度次之
        case .fanboyAnnoyances:
            return 60000   // 干扰元素屏蔽
        case .easyListChina:
            return 50000   // 中国地区特定规则
        }
    }
}

// MARK: - 广告屏蔽统计
struct ContentBlockerStatistics {
    var totalRulesLoaded: Int = 0
    var activeFilterLists: Set<FilterListType> = []
    var lastUpdateDate: Date?
    var blockedRequestsCount: Int = 0
    var memoryUsage: UInt64 = 0
    
    var description: String {
        return """
        已加载规则：\(totalRulesLoaded)
        活跃列表：\(activeFilterLists.count)
        最后更新：\(lastUpdateDate?.formatted(date: .abbreviated, time: .shortened) ?? "未知")
        已屏蔽：\(blockedRequestsCount) 个请求
        内存使用：\(ByteCountFormatter.string(fromByteCount: Int64(memoryUsage), countStyle: .memory))
        """
    }
}

// MARK: - 广告屏蔽配置
struct ContentBlockerConfiguration: Codable {
    var enabledFilterLists: Set<FilterListType> = [.easyListChina, .easyList, .easyPrivacy]  // 默认选中中国地区规则
    var enableAdvancedBlocking: Bool = true
    var optimizeForPerformance: Bool = {
        // 根据设备类型智能调整：iPhone优化性能，iPad保持完整功能
        return UIDevice.current.userInterfaceIdiom == .phone
    }()
    var maxMemoryUsage: UInt64 = {
        // 根据设备内存动态调整
        let physicalMemory = ProcessInfo.processInfo.physicalMemory
        return min(physicalMemory / 10, 150 * 1024 * 1024)  // 10%内存或150MB，取较小值
    }()
    var maxRulesPerList: Int = 50000  // 保守限制，避免WebKit编译失败
    var enableAutomaticFallback: Bool = true  // 编译失败时自动降级
    var detailedLogging: Bool = false  // 详细日志记录
    
    static let `default` = ContentBlockerConfiguration()
    
    // 编码键，确保向后兼容
    enum CodingKeys: String, CodingKey {
        case enabledFilterLists
        case enableAdvancedBlocking
        case optimizeForPerformance
        case maxMemoryUsage
        case maxRulesPerList
        case enableAutomaticFallback
        case detailedLogging
    }
    
    // 自定义解码，处理旧版本兼容性
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        enabledFilterLists = try container.decodeIfPresent(Set<FilterListType>.self, forKey: .enabledFilterLists) ?? [.easyListChina, .easyList, .easyPrivacy]
        enableAdvancedBlocking = try container.decodeIfPresent(Bool.self, forKey: .enableAdvancedBlocking) ?? true
        optimizeForPerformance = try container.decodeIfPresent(Bool.self, forKey: .optimizeForPerformance) ?? (UIDevice.current.userInterfaceIdiom == .phone)
        maxMemoryUsage = try container.decodeIfPresent(UInt64.self, forKey: .maxMemoryUsage) ?? min(ProcessInfo.processInfo.physicalMemory / 10, 150 * 1024 * 1024)
        maxRulesPerList = try container.decodeIfPresent(Int.self, forKey: .maxRulesPerList) ?? 50000
        enableAutomaticFallback = try container.decodeIfPresent(Bool.self, forKey: .enableAutomaticFallback) ?? true
        detailedLogging = try container.decodeIfPresent(Bool.self, forKey: .detailedLogging) ?? false
    }
    
    init() {
        // 使用默认值初始化
    }
}

// MARK: - 内容屏蔽器管理器
@MainActor
final class ContentBlockerManager: ObservableObject {
    static let shared = ContentBlockerManager()
    
    // MARK: - 状态
    @Published private(set) var isEnabled: Bool = true  // 默认启用广告屏蔽
    @Published private(set) var isUpdating: Bool = false
    @Published private(set) var statistics = ContentBlockerStatistics()
    @Published var configuration = ContentBlockerConfiguration.default
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.browser", category: "ContentBlocker")
    private let fileManager = FileManager.default
    private var activeRuleLists: [String: WKContentRuleList] = [:]
    private let networkMonitor = NWPathMonitor()
    private var isNetworkAvailable = false
    
    // MARK: - 存储路径
    private var documentsDirectory: URL {
        fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
    }
    
    private var filterListsDirectory: URL {
        documentsDirectory.appendingPathComponent("FilterLists")
    }
    
    private init() {
        setupNetworkMonitoring()
        createDirectoriesIfNeeded()
        loadSavedConfiguration()
        
        // 应用启动时自动加载已有的编译规则
        Task {
            await loadExistingCompiledRules()
        }
        
        logger.info("✅ ContentBlockerManager 初始化完成，默认启用广告屏蔽")
    }
    
    // MARK: - 公共接口
    
    /// 启用广告屏蔽
    func enableContentBlocking() async throws {
        guard isNetworkAvailable else {
            throw ContentBlockerError.networkUnavailable
        }
        
        isEnabled = true
        // 首先尝试加载已有规则，如果没有再更新
        await loadExistingCompiledRules()
        if activeRuleLists.isEmpty {
            await loadOrUpdateFilterLists()
        }
        logger.info("🛡️ 广告屏蔽已启用")
    }
    
    /// 禁用广告屏蔽
    func disableContentBlocking() {
        isEnabled = false
        activeRuleLists.removeAll() // 只清空内存中的引用，不删除编译后的规则
        logger.info("❌ 广告屏蔽已禁用")
    }
    
    /// 手动更新过滤列表
    func updateFilterLists() async throws {
        guard isNetworkAvailable else {
            throw ContentBlockerError.networkUnavailable
        }
        
        isUpdating = true
        defer { isUpdating = false }
        
        // 清除旧的编译规则
        await clearOldCompiledRules()
        await loadOrUpdateFilterLists()
        logger.info("🔄 过滤列表更新完成")
    }
    
    /// 为WebView配置内容屏蔽
    func configureWebView(_ webView: WKWebView) {
        guard isEnabled else { 
            logger.info("⚠️ 广告屏蔽已禁用，跳过WebView配置")
            return 
        }
        
        // 确保在主线程执行
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // 移除现有规则列表
            webView.configuration.userContentController.removeAllContentRuleLists()
            
            // 检查是否有活跃的规则列表
            if self.activeRuleLists.isEmpty {
                self.logger.warning("⚠️ 没有找到活跃的规则列表，尝试重新加载")
                Task {
                    await self.loadExistingCompiledRules()
                    // 重新配置WebView
                    DispatchQueue.main.async {
                        self.configureWebView(webView)
                    }
                }
                return
            }
            
            // 添加活跃的规则列表
            for (identifier, ruleList) in self.activeRuleLists {
                webView.configuration.userContentController.add(ruleList)
                self.logger.debug("✅ 已为WebView添加规则列表: \(identifier)")
            }
            
            self.logger.info("🛡️ WebView广告屏蔽配置完成，已应用 \(self.activeRuleLists.count) 个规则列表")
            
            // 配置完成后验证
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                self?.notifyWebViewConfigured(webView)
            }
        }
    }
    
    /// 获取可用的编译规则标识符
    func getAvailableRuleListIdentifiers() async -> [String] {
        return await withCheckedContinuation { continuation in
            WKContentRuleListStore.default().getAvailableContentRuleListIdentifiers { identifiers in
                continuation.resume(returning: identifiers ?? [])
            }
        }
    }
    
    /// 安全清除编译规则，避免删除正在使用的规则
    private func clearCompiledRule(identifier: String) async {
        return await withCheckedContinuation { continuation in
            // 首先检查规则是否存在
            WKContentRuleListStore.default().lookUpContentRuleList(forIdentifier: identifier) { ruleList, error in
                if ruleList == nil {
                    // 规则不存在，直接返回
                    self.logger.info("🔍 规则列表不存在，跳过清除: \(identifier)")
                    continuation.resume()
                    return
                }
                
                // 从activeRuleLists中移除引用，避免冲突
                if self.activeRuleLists[identifier] != nil {
                    self.activeRuleLists.removeValue(forKey: identifier)
                    self.logger.debug("📤 已从内存中移除规则列表引用: \(identifier)")
                }
                
                // 延迟删除，确保没有WebView正在使用
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    WKContentRuleListStore.default().removeContentRuleList(forIdentifier: identifier) { error in
                        if let error = error {
                            let nsError = error as NSError
                            if nsError.domain == "WKErrorDomain" && nsError.code == 8 {
                                self.logger.warning("⚠️ 规则列表可能正在使用中，标记为待清理: \(identifier)")
                                // WKErrorDomain错误8通常表示规则正在使用，我们记录但不视为严重错误
                            } else {
                                self.logger.warning("⚠️ 清除编译规则失败 \(identifier): \(error.localizedDescription)")
                            }
                        } else {
                            self.logger.info("🗑️ 已安全清除编译规则: \(identifier)")
                        }
                        continuation.resume()
                    }
                }
            }
        }
    }
    
    /// 清除所有旧的编译规则
    private func clearOldCompiledRules() async {
        let availableIdentifiers = await getAvailableRuleListIdentifiers()
        let copIdentifiers = availableIdentifiers.filter { $0.hasPrefix("cop_") }
        
        logger.info("🗑️ 开始清除 \(copIdentifiers.count) 个旧的编译规则")
        
        // 先清空内存引用
        activeRuleLists.removeAll()
        
        // 分批清除，避免同时删除太多规则
        for identifier in copIdentifiers {
            await clearCompiledRule(identifier: identifier)
            // 添加小延迟，避免并发冲突
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
        }
        
        logger.info("🗑️ 旧规则清除完成")
    }
    
    /// 智能分割大型规则列表
    private func splitRulesIfNeeded(_ rules: [String], for listType: FilterListType) -> [[String]] {
        let maxRulesPerList = configuration.maxRulesPerList
        
        if rules.count <= maxRulesPerList {
            return [rules]
        }
        
        var batches: [[String]] = []
        var currentBatch: [String] = []
        
        for rule in rules {
            currentBatch.append(rule)
            
            if currentBatch.count >= maxRulesPerList {
                batches.append(currentBatch)
                currentBatch = []
            }
        }
        
        if !currentBatch.isEmpty {
            batches.append(currentBatch)
        }
        
        logger.info("📦 规则列表分割: \(listType.displayName) → \(batches.count) 个批次 (每批最多 \(maxRulesPerList) 条规则)")
        return batches
    }
    
    /// 带降级策略的规则编译
    private func compileContentRuleListWithFallback(_ json: String, identifier: String) async throws -> WKContentRuleList {
        do {
            // 首次尝试编译
            return try await compileContentRuleList(json, identifier: identifier)
        } catch {
            let nsError = error as NSError
            
            if nsError.domain == "WKErrorDomain" && nsError.code == 8 && configuration.enableAutomaticFallback {
                logger.warning("⚠️ 规则编译失败，启用降级策略: \(identifier)")
                
                // 降级策略：减少规则数量重试
                if let fallbackRules = try? reduceLargeRuleSet(json) {
                    logger.info("🔄 使用降级规则集重新编译: \(identifier)")
                    return try await compileContentRuleList(fallbackRules, identifier: "\(identifier)_fallback")
                }
            }
            
            throw error
        }
    }
    
    /// 减少规则集大小的降级策略
    private func reduceLargeRuleSet(_ json: String) throws -> String {
        guard let data = json.data(using: .utf8),
              let jsonArray = try JSONSerialization.jsonObject(with: data) as? [[String: Any]] else {
            throw ContentBlockerError.compilationFailed
        }
        
        // 保留最重要的规则类型，优先级：block > block-cookies > ignore-previous-rules
        let prioritizedRules = jsonArray.sorted { rule1, rule2 in
            let action1 = rule1["action"] as? [String: Any]
            let action2 = rule2["action"] as? [String: Any]
            let type1 = action1?["type"] as? String ?? ""
            let type2 = action2?["type"] as? String ?? ""
            
            return getRulePriority(type1) < getRulePriority(type2)
        }
        
        // 保留前75%的规则
        let reducedCount = Int(Double(prioritizedRules.count) * 0.75)
        let reducedRules = Array(prioritizedRules.prefix(reducedCount))
        
        let reducedData = try JSONSerialization.data(withJSONObject: reducedRules)
        return String(data: reducedData, encoding: .utf8) ?? json
    }
    
    /// 获取规则类型优先级
    private func getRulePriority(_ type: String) -> Int {
        switch type {
        case "block": return 1
        case "block-cookies": return 2
        case "css-display-none": return 3
        case "ignore-previous-rules": return 4
        default: return 5
        }
    }

    private func notifyWebViewConfigured(_ webView: WKWebView) {
        // 在WebView配置完成后执行一些验证
        logger.debug("📝 WebView配置验证: 规则列表数量 = \(self.activeRuleLists.count)")
        
        // 优化的JavaScript验证脚本 - 减少可能的验证错误
        let verificationScript = """
        (function() {
            try {
                console.log('🛡️ 广告屏蔽规则验证开始');
                console.log('📊 期望应用的规则列表数量: \(self.activeRuleLists.count)');
                
                // 仅在debug模式下执行详细测试
                if (typeof window.cop_debug !== 'undefined') {
                    // 测试常见广告域名是否被屏蔽
                    const testDomains = [
                        'googleads.g.doubleclick.net',
                        'googlesyndication.com',
                        'google-analytics.com'
                    ];
                    
                    testDomains.forEach(domain => {
                        console.log('🧪 测试屏蔽域名: ' + domain);
                    });
                }
                
                // 验证规则列表标识符
                console.log('🏷️ 活跃规则列表: \(Array(self.activeRuleLists.keys).joined(separator: ", "))');
                console.log('✅ 广告屏蔽规则验证完成');
                
                // 返回验证结果
                return {
                    success: true,
                    ruleListsCount: \(self.activeRuleLists.count),
                    activeRuleLists: '\(Array(self.activeRuleLists.keys).joined(separator: ", "))'
                };
            } catch (error) {
                console.log('⚠️ 验证过程中发生错误:', error.message);
                return {
                    success: false,
                    error: error.message
                };
            }
        })();
        """
        
        // 使用更安全的JavaScript执行方式，添加超时和错误处理
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            guard let self = self else { return }
            
            webView.evaluateJavaScript(verificationScript, completionHandler: { result, error in
                if let error = error {
                    let errorMsg = error.localizedDescription
                    // 过滤掉常见的无害错误
                    if !errorMsg.contains("A JavaScript exception occurred") ||
                       !errorMsg.contains("undefined is not an object") {
                        self.logger.warning("⚠️ JavaScript验证失败: \(errorMsg)")
                        
                        // 使用系统错误处理器处理JavaScript验证错误
                        Task { @MainActor in
                            SystemErrorHandler.shared.handleJavaScriptValidationError(error)
                        }
                    }
                } else {
                    self.logger.info("✅ WebView广告屏蔽验证完成")
                    if let result = result as? [String: Any] {
                        self.logger.debug("📊 验证结果: \(result)")
                    }
                }
            })
        }
        
        // 额外验证：如果有活跃规则但WebView配置可能有问题，记录警告
        if !self.activeRuleLists.isEmpty {
            logger.info("🛡️ WebView已配置 \(self.activeRuleLists.count) 个广告屏蔽规则列表")
            for (identifier, _) in self.activeRuleLists {
                logger.debug("   - \(identifier)")
            }
        } else {
            logger.warning("⚠️ 没有活跃的广告屏蔽规则列表！")
        }
    }
    
    /// 获取统计信息
    func getStatistics() -> ContentBlockerStatistics {
        updateStatistics()
        return statistics
    }
    
    // MARK: - 私有方法
    
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isNetworkAvailable = path.status == .satisfied
            }
        }
        let queue = DispatchQueue(label: "NetworkMonitor")
        networkMonitor.start(queue: queue)
    }
    
    private func createDirectoriesIfNeeded() {
        try? fileManager.createDirectory(at: filterListsDirectory, withIntermediateDirectories: true)
    }
    
    private func loadSavedConfiguration() {
        let configPath = documentsDirectory.appendingPathComponent("ContentBlockerConfig.json")
        guard let data = try? Data(contentsOf: configPath),
              let config = try? JSONDecoder().decode(ContentBlockerConfiguration.self, from: data) else {
            return
        }
        configuration = config
    }
    
    private func saveConfiguration() {
        let configPath = documentsDirectory.appendingPathComponent("ContentBlockerConfig.json")
        guard let data = try? JSONEncoder().encode(configuration) else { return }
        try? data.write(to: configPath)
    }
    
    private func loadOrUpdateFilterLists() async {
        // 避免重复加载已经存在的规则
        let existingIdentifiers = Set(activeRuleLists.keys.map { $0.replacingOccurrences(of: "cop_", with: "") })
        let listsToLoad = configuration.enabledFilterLists.filter { !existingIdentifiers.contains($0.rawValue) }
        
        for listType in listsToLoad {
            await loadFilterList(listType)
        }
        updateStatistics()
    }
    
    private func loadFilterList(_ listType: FilterListType) async {
        let identifier = "cop_\(listType.rawValue)"
        
        // 检查是否已经加载
        if activeRuleLists[identifier] != nil {
            logger.info("ℹ️ 规则列表已存在，跳过加载: \(listType.displayName)")
            return
        }
        
        do {
            // 1. 下载或读取缓存的过滤列表
            let filterContent = try await downloadOrLoadCachedFilterList(listType)
            
            // 2. 解析和优化规则
            let optimizedRules = try parseAndOptimizeRules(filterContent, for: listType)
            
            // 3. 智能分割大型规则列表
            let ruleBatches = splitRulesIfNeeded(optimizedRules, for: listType)
            
            // 4. 先清除可能存在的旧规则
            await clearCompiledRule(identifier: identifier)
            
            // 5. 处理规则批次
            if ruleBatches.count == 1 {
                // 单个规则列表，正常处理
                let contentRuleJSON = try convertToContentRuleListJSON(ruleBatches[0], for: listType)
                let ruleList = try await compileContentRuleListWithFallback(contentRuleJSON, identifier: identifier)
                
                await MainActor.run { [weak self] in
                    self?.activeRuleLists[identifier] = ruleList
                }
                
                logger.info("✅ 过滤列表加载成功: \(listType.displayName) (\(optimizedRules.count) 条规则)")
            } else {
                // 多个规则批次，分别编译
                var loadedCount = 0
                for (index, batch) in ruleBatches.enumerated() {
                    let batchIdentifier = "\(identifier)_\(index)"
                    let contentRuleJSON = try convertToContentRuleListJSON(batch, for: listType)
                    
                    if let ruleList = try? await compileContentRuleListWithFallback(contentRuleJSON, identifier: batchIdentifier) {
                        await MainActor.run { [weak self] in
                            self?.activeRuleLists[batchIdentifier] = ruleList
                        }
                        loadedCount += batch.count
                    }
                }
                
                logger.info("✅ 分批过滤列表加载成功: \(listType.displayName) (\(loadedCount)/\(optimizedRules.count) 条规则，\(ruleBatches.count) 个批次)")
            }
            
        } catch {
            logger.error("❌ 过滤列表加载失败: \(listType.displayName), 错误: \(error.localizedDescription)")
            
            // 如果是编译错误，尝试使用更少的规则重新编译
            if error.localizedDescription.contains("WKErrorDomain error 7") {
                logger.info("🔄 检测到编译错误，尝试使用减少的规则重新编译...")
                await retryWithReducedRules(listType)
            }
        }
    }
    
    private func downloadOrLoadCachedFilterList(_ listType: FilterListType) async throws -> String {
        let cacheFile = filterListsDirectory.appendingPathComponent("\(listType.rawValue).txt")
        
        // 尝试从缓存加载
        if fileManager.fileExists(atPath: cacheFile.path) {
            let cacheDate = try fileManager.attributesOfItem(atPath: cacheFile.path)[.modificationDate] as? Date
            let daysSinceUpdate = Date().timeIntervalSince(cacheDate ?? Date()) / (24 * 60 * 60)
            
            // 如果缓存不超过7天，使用缓存
            if daysSinceUpdate < 7 {
                logger.info("📁 使用缓存的过滤列表: \(listType.displayName)")
                return try String(contentsOf: cacheFile, encoding: .utf8)
            }
        }
        
        // 下载新版本 - 首先尝试主URL
        var urls: [URL] = []
        if let mainURL = listType.downloadURL {
            urls = [mainURL] + listType.alternativeDownloadURLs
        } else {
            urls = listType.alternativeDownloadURLs
        }
        
        guard !urls.isEmpty else {
            throw ContentBlockerError.networkUnavailable
        }
        
        var lastError: Error?
        
        // 尝试所有可用URL
        for url in urls {
            do {
                logger.info("🌐 正在下载过滤列表: \(listType.displayName) from \(url.absoluteString)")
                
                var request = URLRequest(url: url, timeoutInterval: 30.0)
                request.setValue("gzip, deflate", forHTTPHeaderField: "Accept-Encoding")
                request.setValue("text/plain, */*", forHTTPHeaderField: "Accept")
                request.setValue("Mozilla/5.0 (iPad; CPU OS 18_4 like Mac OS X) AppleWebKit/605.1.15", forHTTPHeaderField: "User-Agent")
                request.setValue("no-cache", forHTTPHeaderField: "Cache-Control")
                
                let (data, response) = try await URLSession.shared.data(for: request)
                
                // 检查HTTP响应状态
                if let httpResponse = response as? HTTPURLResponse {
                    guard httpResponse.statusCode == 200 else {
                        throw ContentBlockerError.networkError("HTTP \(httpResponse.statusCode)")
                    }
                }
                
                // 尝试多种编码方式解码数据
                var content: String?
                
                // 首先尝试UTF-8
                content = String(data: data, encoding: .utf8)
                
                // 如果UTF-8失败，尝试其他编码
                if content == nil {
                    content = String(data: data, encoding: .ascii)
                }
                
                if content == nil {
                    content = String(data: data, encoding: .iso2022JP)
                }
                
                guard let validContent = content, !validContent.isEmpty else {
                    throw ContentBlockerError.networkError("无法解码下载的数据")
                }
                
                // 验证内容格式
                let lines = validContent.components(separatedBy: .newlines)
                let validLines = lines.filter { !$0.trimmingCharacters(in: .whitespaces).isEmpty }
                
                guard validLines.count > 10 else { // 至少要有10行有效内容
                    throw ContentBlockerError.networkError("下载的内容格式无效")
                }
                
                // 保存到缓存
                try validContent.write(to: cacheFile, atomically: true, encoding: .utf8)
                
                logger.info("✅ 过滤列表下载成功: \(listType.displayName)，\(validLines.count) 行内容")
                return validContent
                
            } catch {
                lastError = error
                logger.warning("⚠️ URL失败 \(url.absoluteString): \(error.localizedDescription)")
                continue
            }
        }
        
        throw lastError ?? ContentBlockerError.networkError("所有下载URL都失败")
    }
    
    private func parseAndOptimizeRules(_ content: String, for listType: FilterListType, maxRules: Int? = nil) throws -> [String] {
        let lines = content.components(separatedBy: .newlines)
        var rules: [String] = []
        let actualMaxRules = maxRules ?? listType.maxRules
        
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // 跳过注释和空行
            guard !trimmedLine.isEmpty,
                  !trimmedLine.hasPrefix("!"),
                  !trimmedLine.hasPrefix("[") else {
                continue
            }
            
            // 过滤掉可能导致合法网站无法访问的过于宽泛的规则
            if !isValidRule(trimmedLine) {
                continue
            }
            
            rules.append(trimmedLine)
            
            // 限制规则数量以避免超出iOS限制
            if rules.count >= actualMaxRules {
                break
            }
        }
        
        logger.info("📊 \(listType.displayName): 解析了 \(rules.count) 条有效规则 (最大限制: \(actualMaxRules))")
        return rules
    }
    
    /// 验证规则是否合理（避免过于宽泛的规则）
    private func isValidRule(_ rule: String) -> Bool {
        // 跳过可能影响正常导航的规则
        let problematicPatterns = [
            "@@||*", // 过于宽泛的白名单
            "||*^", // 过于宽泛的域名匹配
            "*$popup,third-party", // 可能影响正常弹窗
        ]
        
        for pattern in problematicPatterns {
            if rule.hasPrefix(pattern) {
                return false
            }
        }
        
        return true
    }
    
    private func convertToContentRuleListJSON(_ rules: [String], for listType: FilterListType) throws -> String {
        let rulesContent = rules.joined(separator: "\n")
        guard let result = EasyListConverter.convertToWKContentRuleList(from: rulesContent) else {
            throw ContentBlockerError.parseFailed
        }
        return result
    }
    
    private func compileContentRuleList(_ jsonString: String, identifier: String) async throws -> WKContentRuleList {
        return try await withCheckedThrowingContinuation { continuation in
            WKContentRuleListStore.default().compileContentRuleList(
                forIdentifier: identifier,
                encodedContentRuleList: jsonString
            ) { ruleList, error in
                if let error = error {
                    continuation.resume(throwing: error)
                } else if let ruleList = ruleList {
                    continuation.resume(returning: ruleList)
                } else {
                    continuation.resume(throwing: ContentBlockerError.compilationFailed)
                }
            }
        }
    }
    
    /// 移除所有内容规则列表（完全清理，包括磁盘上的编译规则）
    private func removeAllContentRuleLists() async {
        activeRuleLists.removeAll()
        
        let availableIdentifiers = await getAvailableRuleListIdentifiers()
        let copIdentifiers = availableIdentifiers.filter { $0.hasPrefix("cop_") }
        
        for identifier in copIdentifiers {
            await clearCompiledRule(identifier: identifier)
        }
        
        logger.info("🗑️ 已完全清除所有内容规则列表")
    }
    
    private func updateStatistics() {
        var totalRules = 0
        var activeList: Set<FilterListType> = []
        var ruleBreakdown: [String] = []
        
        // 统计活跃的规则列表和实际使用的规则数量
        for (identifier, _) in activeRuleLists {
            // 从标识符中提取过滤列表类型
            let listTypeString = identifier.replacingOccurrences(of: "cop_", with: "")
            if let listType = FilterListType(rawValue: listTypeString) {
                activeList.insert(listType)
                
                // 读取实际编译的规则数量（从缓存文件统计）
                if let actualRuleCount = getActualRuleCount(for: listType) {
                    totalRules += actualRuleCount
                    ruleBreakdown.append("\(listType.displayName): \(actualRuleCount)")
                    logger.debug("📊 \(listType.displayName): \(actualRuleCount) 条实际规则")
                } else {
                    // 备选方案：使用保守估算，但标记为估算值
                    let estimatedRules = min(listType.maxRules / 3, 30000) // 更保守的估算
                    totalRules += estimatedRules
                    ruleBreakdown.append("\(listType.displayName): ~\(estimatedRules)")
                    logger.debug("📊 \(listType.displayName): ~\(estimatedRules) 条估算规则")
                }
            }
        }
        
        // 获取内存使用情况
        let memoryUsage = getMemoryUsage()
        
        // 更新统计信息，避免重复累计
        statistics = ContentBlockerStatistics(
            totalRulesLoaded: totalRules,
            activeFilterLists: activeList,
            lastUpdateDate: getLastUpdateDate(),
            blockedRequestsCount: statistics.blockedRequestsCount, // 保持之前的计数
            memoryUsage: memoryUsage
        )
        
        let breakdownStr = ruleBreakdown.joined(separator: ", ")
        logger.info("📊 统计信息更新: \(totalRules) 条规则，\(activeList.count) 个活跃列表 [\(breakdownStr)]")
    }
    
    private func getActualRuleCount(for listType: FilterListType) -> Int? {
        let cacheFile = filterListsDirectory.appendingPathComponent("\(listType.rawValue).txt")
        guard fileManager.fileExists(atPath: cacheFile.path),
              let content = try? String(contentsOf: cacheFile, encoding: .utf8) else {
            return nil
        }
        
        // 计算有效规则行数
        let lines = content.components(separatedBy: .newlines)
        let validRules = lines.filter { line in
            let trimmed = line.trimmingCharacters(in: .whitespaces)
            return !trimmed.isEmpty && !trimmed.hasPrefix("!")
        }
        
        return validRules.count
    }
    
    private func getLastUpdateDate() -> Date? {
        var latestDate: Date?
        
        for listType in FilterListType.allCases {
            let cacheFile = filterListsDirectory.appendingPathComponent("\(listType.rawValue).txt")
            if let attributes = try? fileManager.attributesOfItem(atPath: cacheFile.path),
               let modDate = attributes[.modificationDate] as? Date {
                if latestDate == nil || modDate > latestDate! {
                    latestDate = modDate
                }
            }
        }
        
        return latestDate
    }
    
    private func getMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        return result == KERN_SUCCESS ? info.resident_size : 0
    }
    
    private func loadExistingCompiledRules() async {
        guard isEnabled else { return }
        
        logger.info("🔄 正在加载已有的编译规则...")
        
        // 获取所有可用的编译规则标识符
        let availableIdentifiers = await getAvailableRuleListIdentifiers()
        let copIdentifiers = availableIdentifiers.filter { $0.hasPrefix("cop_") }
        
        if copIdentifiers.isEmpty {
            logger.info("ℹ️ 没有找到已编译的规则列表")
            return
        }
        
        await withTaskGroup(of: Void.self) { group in
            for identifier in copIdentifiers {
                group.addTask { [weak self] in
                    await self?.loadCompiledRuleListByIdentifier(identifier)
                }
            }
        }
        
        updateStatistics()
        logger.info("✅ 已加载 \(self.activeRuleLists.count) 个编译规则列表")
    }
    
    private func loadCompiledRuleListByIdentifier(_ identifier: String) async {
        do {
            let ruleList = try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<WKContentRuleList, Error>) in
                WKContentRuleListStore.default().lookUpContentRuleList(forIdentifier: identifier) { ruleList, error in
                    if let error = error {
                        continuation.resume(throwing: error)
                    } else if let ruleList = ruleList {
                        continuation.resume(returning: ruleList)
                    } else {
                        continuation.resume(throwing: ContentBlockerError.ruleListNotFound)
                    }
                }
            }
            
            await MainActor.run { [weak self] in
                guard let self = self else { return }
                self.activeRuleLists[identifier] = ruleList
                let listName = identifier.replacingOccurrences(of: "cop_", with: "")
                self.logger.info("✅ 已加载编译规则: \(listName)")
            }
        } catch {
            let listName = identifier.replacingOccurrences(of: "cop_", with: "")
            logger.warning("⚠️ 无法加载编译规则 \(listName): \(error.localizedDescription)")
            
            // 如果加载失败，尝试重新下载和编译
            if let listType = FilterListType(rawValue: listName),
               configuration.enabledFilterLists.contains(listType) {
                do {
                    logger.info("🔄 尝试重新下载和编译: \(listType.displayName)")
                    try await downloadAndCompileFilterList(listType)
                } catch {
                    logger.error("❌ 重新编译也失败: \(listType.displayName) - \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func downloadAndCompileFilterList(_ listType: FilterListType) async throws {
        // 下载过滤列表
        let content = try await downloadOrLoadCachedFilterList(listType)
        
        // 转换为JSON
        guard let jsonString = EasyListConverter.convertToWKContentRuleList(from: content) else {
            throw ContentBlockerError.compilationFailed
        }
        
        // 编译规则列表
        let identifier = "cop_\(listType.rawValue)"
        let ruleList = try await compileContentRuleList(jsonString, identifier: identifier)
        
        await MainActor.run { [weak self] in
            guard let self = self else { return }
            self.activeRuleLists[identifier] = ruleList
            self.logger.info("✅ 成功下载并编译规则: \(listType.displayName)")
        }
    }
    
    /// 使用减少的规则重新编译
    private func retryWithReducedRules(_ listType: FilterListType) async {
        let identifier = "cop_\(listType.rawValue)"
        
        do {
            let filterContent = try await downloadOrLoadCachedFilterList(listType)
            
            // 使用更保守的规则数量
            let conservativeMaxRules = min(listType.maxRules / 2, 25000)
            let limitedRules = try parseAndOptimizeRules(filterContent, for: listType, maxRules: conservativeMaxRules)
            
            let contentRuleJSON = try convertToContentRuleListJSON(limitedRules, for: listType)
            
            // 清除旧规则并编译新的
            await clearCompiledRule(identifier: identifier)
            let ruleList = try await compileContentRuleList(contentRuleJSON, identifier: identifier)
            
            await MainActor.run { [weak self] in
                self?.activeRuleLists[identifier] = ruleList
            }
            
            logger.info("✅ 使用减少规则重新编译成功: \(listType.displayName) (\(limitedRules.count) 条规则)")
            
        } catch {
            logger.error("❌ 减少规则重新编译也失败: \(listType.displayName), 错误: \(error.localizedDescription)")
        }
    }
}

// MARK: - 错误类型
enum ContentBlockerError: LocalizedError {
    case networkUnavailable
    case invalidURL
    case downloadFailed
    case parseFailed
    case compilationFailed
    case memoryLimitExceeded
    case ruleListNotFound
    case networkError(String)
    
    var errorDescription: String? {
        switch self {
        case .networkUnavailable:
            return "网络不可用"
        case .invalidURL:
            return "无效的URL"
        case .downloadFailed:
            return "下载失败"
        case .parseFailed:
            return "解析失败"
        case .compilationFailed:
            return "规则编译失败"
        case .memoryLimitExceeded:
            return "内存限制超出"
        case .ruleListNotFound:
            return "规则列表未找到"
        case .networkError(let message):
            return message
        }
    }
}

// MARK: - Codable支持
extension FilterListType: Codable {} 