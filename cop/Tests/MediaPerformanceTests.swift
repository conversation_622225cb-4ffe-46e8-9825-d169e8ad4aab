//
//  MediaPerformanceTests.swift
//  copTests
//
//  Created by Augment Agent on 2025-06-18.
//  OPTIMIZED: 多媒体管理性能测试套件
//

import XCTest
import UIKit
import AVFoundation
@testable import cop

class MediaPerformanceTests: XCTestCase {
    
    var mediaImportService: OptimizedMediaImportService!
    var thumbnailCache: OptimizedThumbnailCache!
    var databaseManager: MediaDatabaseManager!
    
    // 测试数据
    var testMediaFiles: [MediaFileInfo] = []
    var testImageURLs: [URL] = []
    var testVideoURLs: [URL] = []
    
    override func setUpWithError() throws {
        try super.setUpWithError()
        
        mediaImportService = OptimizedMediaImportService.shared
        thumbnailCache = OptimizedThumbnailCache.shared
        databaseManager = MediaDatabaseManager.shared
        
        // 生成测试数据
        generateTestData()
    }
    
    override func tearDownWithError() throws {
        // 清理测试数据
        await thumbnailCache.clearAllCaches()
        
        try super.tearDownWithError()
    }
    
    // MARK: - 内存性能测试
    
    func testMemoryUsageDuringBatchImport() throws {
        measure(metrics: [XCTMemoryMetric()]) {
            let expectation = XCTestExpectation(description: "批量导入完成")
            
            Task {
                do {
                    // 模拟导入1000个文件
                    let testFiles = generateMockMediaFiles(count: 1000)
                    
                    let startMemory = getCurrentMemoryUsage()
                    
                    // 执行批量导入
                    _ = try await mediaImportService.importMediaFolder(from: createTestDirectory())
                    
                    let endMemory = getCurrentMemoryUsage()
                    let memoryIncrease = endMemory - startMemory
                    
                    // 验证内存增长不超过200MB
                    XCTAssertLessThan(memoryIncrease, 200 * 1024 * 1024, "内存使用超出预期")
                    
                    expectation.fulfill()
                } catch {
                    XCTFail("导入失败: \(error)")
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 30.0)
        }
    }
    
    func testThumbnailCacheMemoryEfficiency() throws {
        measure(metrics: [XCTMemoryMetric()]) {
            let expectation = XCTestExpectation(description: "缩略图缓存测试完成")
            
            Task {
                let startMemory = getCurrentMemoryUsage()
                
                // 加载200个缩略图
                for i in 0..<200 {
                    let mediaFile = testMediaFiles[i % testMediaFiles.count]
                    _ = await thumbnailCache.loadThumbnail(for: mediaFile.id)
                }
                
                let endMemory = getCurrentMemoryUsage()
                let memoryIncrease = endMemory - startMemory
                
                // 验证内存增长不超过50MB
                XCTAssertLessThan(memoryIncrease, 50 * 1024 * 1024, "缩略图缓存内存使用过高")
                
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 15.0)
        }
    }
    
    // MARK: - 帧率性能测试
    
    func testScrollingPerformance() throws {
        let collectionView = createTestCollectionView()
        
        measure(metrics: [XCTCPUMetric(), XCTClockMetric()]) {
            // 模拟快速滚动
            for i in 0..<100 {
                let indexPath = IndexPath(item: i % testMediaFiles.count, section: 0)
                
                // 模拟cell出现
                let cell = collectionView.cellForItem(at: indexPath)
                
                // 触发缩略图加载
                let mediaFile = testMediaFiles[indexPath.item]
                
                let expectation = XCTestExpectation(description: "缩略图加载完成")
                Task {
                    _ = await thumbnailCache.loadThumbnail(for: mediaFile.id)
                    expectation.fulfill()
                }
                
                wait(for: [expectation], timeout: 0.1) // 100ms内完成
            }
        }
    }
    
    func testLazyLoadingPerformance() throws {
        measure(metrics: [XCTClockMetric()]) {
            let expectation = XCTestExpectation(description: "懒加载测试完成")
            
            Task {
                // 模拟分页加载
                let pageSize = 50
                var loadedItems = 0
                
                for page in 0..<10 {
                    let offset = page * pageSize
                    let mediaFiles = await databaseManager.loadMediaFiles(
                        offset: offset,
                        limit: pageSize
                    )
                    
                    loadedItems += mediaFiles.count
                    
                    // 验证每页加载时间不超过50ms
                    let startTime = CFAbsoluteTimeGetCurrent()
                    
                    // 预加载缩略图
                    let mediaFileIDs = mediaFiles.map { $0.id }
                    await thumbnailCache.preloadThumbnails(for: mediaFileIDs)
                    
                    let loadTime = CFAbsoluteTimeGetCurrent() - startTime
                    XCTAssertLessThan(loadTime, 0.05, "分页加载时间过长")
                }
                
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
    }
    
    // MARK: - IO吞吐量测试
    
    func testDatabaseInsertPerformance() throws {
        measure(metrics: [XCTStorageMetric(), XCTClockMetric()]) {
            let expectation = XCTestExpectation(description: "数据库插入测试完成")
            
            Task {
                do {
                    let testFiles = generateMockMediaFiles(count: 1000)
                    
                    let startTime = CFAbsoluteTimeGetCurrent()
                    
                    // 批量插入
                    try await databaseManager.insertMediaFiles(testFiles)
                    
                    let insertTime = CFAbsoluteTimeGetCurrent() - startTime
                    
                    // 验证1000条记录插入时间不超过1秒
                    XCTAssertLessThan(insertTime, 1.0, "数据库插入性能不达标")
                    
                    expectation.fulfill()
                } catch {
                    XCTFail("数据库插入失败: \(error)")
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 5.0)
        }
    }
    
    func testSearchPerformance() throws {
        measure(metrics: [XCTClockMetric()]) {
            let expectation = XCTestExpectation(description: "搜索性能测试完成")
            
            Task {
                // 预先插入测试数据
                let testFiles = generateMockMediaFiles(count: 10000)
                try? await databaseManager.insertMediaFiles(testFiles)
                
                let searchQueries = ["test", "image", "video", "photo", "media"]
                
                for query in searchQueries {
                    let startTime = CFAbsoluteTimeGetCurrent()
                    
                    let results = await databaseManager.searchMediaFiles(query: query)
                    
                    let searchTime = CFAbsoluteTimeGetCurrent() - startTime
                    
                    // 验证搜索时间不超过50ms
                    XCTAssertLessThan(searchTime, 0.05, "搜索性能不达标: \(query)")
                    XCTAssertGreaterThan(results.count, 0, "搜索结果为空: \(query)")
                }
                
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
    }
    
    func testThumbnailGenerationPerformance() throws {
        measure(metrics: [XCTClockMetric(), XCTCPUMetric()]) {
            let expectation = XCTestExpectation(description: "缩略图生成测试完成")
            
            Task {
                let testImages = createTestImages(count: 50)
                
                for imageURL in testImages {
                    let startTime = CFAbsoluteTimeGetCurrent()
                    
                    // 生成缩略图
                    let mediaFile = createMockMediaFile(url: imageURL)
                    _ = await thumbnailCache.loadThumbnail(for: mediaFile.id)
                    
                    let generationTime = CFAbsoluteTimeGetCurrent() - startTime
                    
                    // 验证单个缩略图生成时间不超过100ms
                    XCTAssertLessThan(generationTime, 0.1, "缩略图生成时间过长")
                }
                
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 20.0)
        }
    }
    
    // MARK: - 并发性能测试
    
    func testConcurrentThumbnailLoading() throws {
        measure(metrics: [XCTClockMetric()]) {
            let expectation = XCTestExpectation(description: "并发缩略图加载完成")
            expectation.expectedFulfillmentCount = 100
            
            // 并发加载100个缩略图
            for i in 0..<100 {
                Task {
                    let mediaFile = testMediaFiles[i % testMediaFiles.count]
                    _ = await thumbnailCache.loadThumbnail(for: mediaFile.id)
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 5.0)
        }
    }
    
    func testConcurrentDatabaseOperations() throws {
        measure(metrics: [XCTClockMetric()]) {
            let expectation = XCTestExpectation(description: "并发数据库操作完成")
            expectation.expectedFulfillmentCount = 10
            
            // 并发执行10个数据库操作
            for i in 0..<10 {
                Task {
                    let testFiles = generateMockMediaFiles(count: 100)
                    try? await databaseManager.insertMediaFiles(testFiles)
                    expectation.fulfill()
                }
            }
            
            wait(for: [expectation], timeout: 10.0)
        }
    }
    
    // MARK: - 辅助方法
    
    private func generateTestData() {
        testMediaFiles = generateMockMediaFiles(count: 100)
        testImageURLs = createTestImages(count: 50)
        testVideoURLs = createTestVideos(count: 20)
    }
    
    private func generateMockMediaFiles(count: Int) -> [MediaFileInfo] {
        var files: [MediaFileInfo] = []
        
        for i in 0..<count {
            let mediaFile = MediaFileInfo(
                id: UUID(),
                name: "test_file_\(i).jpg",
                type: .image,
                fileSize: Int64.random(in: 1024...10*1024*1024),
                creationDate: Date(),
                modificationDate: Date(),
                localURL: URL(fileURLWithPath: "/tmp/test_\(i).jpg"),
                thumbnailURL: nil,
                folderPath: "TestFolder",
                duration: nil,
                dimensions: CGSize(width: 1920, height: 1080)
            )
            files.append(mediaFile)
        }
        
        return files
    }
    
    private func createTestImages(count: Int) -> [URL] {
        var urls: [URL] = []
        
        for i in 0..<count {
            let url = URL(fileURLWithPath: "/tmp/test_image_\(i).jpg")
            urls.append(url)
        }
        
        return urls
    }
    
    private func createTestVideos(count: Int) -> [URL] {
        var urls: [URL] = []
        
        for i in 0..<count {
            let url = URL(fileURLWithPath: "/tmp/test_video_\(i).mp4")
            urls.append(url)
        }
        
        return urls
    }
    
    private func createTestDirectory() -> URL {
        return URL(fileURLWithPath: "/tmp/test_media_folder")
    }
    
    private func createTestCollectionView() -> UICollectionView {
        let layout = UICollectionViewFlowLayout()
        layout.itemSize = CGSize(width: 160, height: 200)
        
        let collectionView = UICollectionView(frame: CGRect(x: 0, y: 0, width: 375, height: 667), collectionViewLayout: layout)
        return collectionView
    }
    
    private func createMockMediaFile(url: URL) -> MediaFileInfo {
        return MediaFileInfo(
            id: UUID(),
            name: url.lastPathComponent,
            type: .image,
            fileSize: 1024*1024,
            creationDate: Date(),
            modificationDate: Date(),
            localURL: url,
            thumbnailURL: nil,
            folderPath: "TestFolder",
            duration: nil,
            dimensions: CGSize(width: 1920, height: 1080)
        )
    }
    
    private func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }
        
        return kerr == KERN_SUCCESS ? info.resident_size : 0
    }
}

// MARK: - 性能基准测试
extension MediaPerformanceTests {
    
    func testPerformanceBaseline() throws {
        // 建立性能基准线
        let metrics: [XCTMetric] = [
            XCTClockMetric(),
            XCTCPUMetric(),
            XCTMemoryMetric(),
            XCTStorageMetric()
        ]
        
        let measureOptions = XCTMeasureOptions.default
        measureOptions.iterationCount = 5
        
        measure(metrics: metrics, options: measureOptions) {
            let expectation = XCTestExpectation(description: "基准测试完成")
            
            Task {
                // 综合性能测试
                let testFiles = generateMockMediaFiles(count: 500)
                
                // 1. 数据库操作
                try? await databaseManager.insertMediaFiles(testFiles)
                
                // 2. 搜索操作
                _ = await databaseManager.searchMediaFiles(query: "test")
                
                // 3. 缩略图加载
                let mediaFileIDs = Array(testFiles.prefix(50)).map { $0.id }
                await thumbnailCache.preloadThumbnails(for: mediaFileIDs)
                
                expectation.fulfill()
            }
            
            wait(for: [expectation], timeout: 30.0)
        }
    }
}
