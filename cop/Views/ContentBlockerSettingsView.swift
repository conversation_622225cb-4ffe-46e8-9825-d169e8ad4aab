//
//  ContentBlockerSettingsView.swift
//  cop
//
//  Created by Augment Agent on 2025/6/15.
//

import SwiftUI
import OSLog

struct ContentBlockerSettingsView: View {
    @StateObject private var contentBlockerManager = ContentBlockerManager.shared
    @State private var showingUpdateAlert = false
    @State private var updateError: Error?
    @State private var isTestingBlocker = false
    @State private var testResults: [String] = []
    @State private var showIntegrationGuide = false
    @State private var detailedLogging = false
    @State private var showAdvancedSettings = false
    
    private let logger = Logger(subsystem: "com.cop.browser", category: "ContentBlockerSettings")
    
    var body: some View {
        List {
            // 广告屏蔽状态总览卡片
            Section {
                adBlockerStatusCard
            }
            .listRowInsets(EdgeInsets())
            .listRowBackground(Color.clear)
            
            // 统计信息卡片组
            Section {
                statisticsCardsGrid
            }
            .listRowInsets(EdgeInsets())
            .listRowBackground(Color.clear)
            
                            // 过滤列表控制
            if contentBlockerManager.isEnabled {
                Section {
                    ForEach(FilterListType.allCases, id: \.self) { listType in
                        FilterListRow(
                            listType: listType,
                            isEnabled: contentBlockerManager.configuration.enabledFilterLists.contains(listType),
                            onToggle: { isEnabled in
                                toggleFilterList(listType, enabled: isEnabled)
                            }
                        )
                    }
                    
                    // 手动更新过滤列表按钮
                    manualUpdateSection
                } header: {
                    Text("过滤列表")
                        .font(AppDesignSystem.Typography.sectionHeader)
                        .foregroundColor(AppDesignSystem.Colors.textPrimary)
                } footer: {
                    Text("选择要启用的过滤列表。iOS 18支持每个列表最多150,000条规则，获得更强大的屏蔽效果。")
                        .font(AppDesignSystem.Typography.footnote)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                }
                
                // 广告屏蔽测试
                Section {
                    adBlockTestSection
                } header: {
                    Text("屏蔽效果测试")
                        .font(AppDesignSystem.Typography.sectionHeader)
                        .foregroundColor(AppDesignSystem.Colors.textPrimary)
                }
                
                // 高级设置（可折叠）
                if showAdvancedSettings {
                    Section {
                        advancedSettingsContent
                    } header: {
                        Text("高级设置")
                            .font(AppDesignSystem.Typography.sectionHeader)
                            .foregroundColor(AppDesignSystem.Colors.textPrimary)
                    }
                }
                
                // 高级设置切换
                Section {
                    Button(action: { 
                        withAnimation(AppDesignSystem.Animation.standard) {
                            showAdvancedSettings.toggle()
                        }
                    }) {
                        HStack(spacing: AppDesignSystem.Spacing.md) {
                            ZStack {
                                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.sm)
                                    .fill(AppDesignSystem.Colors.warning.opacity(0.1))
                                    .frame(width: 40, height: 40)
                                
                                Image(systemName: "gearshape.fill")
                                    .font(.system(size: 18, weight: .medium))
                                    .foregroundColor(AppDesignSystem.Colors.warning)
                            }
                            
                            VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                                Text(showAdvancedSettings ? "隐藏高级设置" : "显示高级设置")
                                    .font(AppDesignSystem.Typography.body)
                                    .foregroundColor(AppDesignSystem.Colors.textPrimary)
                                
                                Text("SafariConverterLib 状态、详细配置选项")
                                    .font(AppDesignSystem.Typography.caption1)
                                    .foregroundColor(AppDesignSystem.Colors.textSecondary)
                            }
                            
                            Spacer()
                            
                            Image(systemName: showAdvancedSettings ? "chevron.down" : "chevron.right")
                                .foregroundColor(AppDesignSystem.Colors.iconSecondary)
                                .font(.system(size: 14, weight: .medium))
                                .animation(AppDesignSystem.Animation.quick, value: showAdvancedSettings)
                        }
                        .padding(AppDesignSystem.Spacing.md)
                        .background(
                            RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.md)
                                .fill(AppDesignSystem.Colors.backgroundSecondary)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.md)
                                .stroke(AppDesignSystem.Colors.warning.opacity(0.2), lineWidth: 1)
                        )
                    }
                }
                .listRowInsets(EdgeInsets())
                .listRowBackground(Color.clear)
            }
        }
        .navigationTitle("广告屏蔽")
        .navigationBarTitleDisplayMode(.large)
        .alert("更新结果", isPresented: $showingUpdateAlert) {
            Button("确定") { }
        } message: {
            if let error = updateError {
                Text("更新失败：\(error.localizedDescription)")
            } else {
                Text("过滤列表更新成功！")
            }
        }
        .sheet(isPresented: $showIntegrationGuide) {
            integrationGuideSheet
        }
        .onAppear {
            // 刷新统计信息
            _ = contentBlockerManager.getStatistics()
        }
    }
    
    // MARK: - 广告屏蔽状态卡片
    private var adBlockerStatusCard: some View {
        VStack(spacing: AppDesignSystem.Spacing.lg) {
            HStack {
                // 状态指示器
                ZStack {
                    Circle()
                        .fill(contentBlockerManager.isEnabled ? AppDesignSystem.Colors.success : AppDesignSystem.Colors.warning)
                        .frame(width: 56, height: 56)
                    
                    Image(systemName: contentBlockerManager.isEnabled ? "shield.fill" : "shield.slash")
                        .font(.system(size: 24, weight: .semibold))
                        .foregroundColor(.white)
                }
                
                VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                    Text("广告屏蔽")
                        .font(AppDesignSystem.Typography.title2)
                        .foregroundColor(AppDesignSystem.Colors.textPrimary)
                    
                    Text(contentBlockerManager.isEnabled ? "已启用 - 保护您的浏览体验" : "已禁用 - 点击启用保护")
                        .font(AppDesignSystem.Typography.callout)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                }
                
                Spacer()
            }
            
            // 控制按钮
            Button(action: toggleContentBlocking) {
                HStack {
                    Image(systemName: contentBlockerManager.isEnabled ? "stop.circle" : "play.circle.fill")
                        .font(.system(size: 18, weight: .medium))
                    
                    Text(contentBlockerManager.isEnabled ? "禁用屏蔽" : "启用屏蔽")
                        .font(AppDesignSystem.Typography.buttonLabel)
                }
                .frame(maxWidth: .infinity)
                .frame(height: AppDesignSystem.Spacing.buttonHeight)
                .background(
                    RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.button)
                        .fill(contentBlockerManager.isEnabled ? AppDesignSystem.Colors.error : AppDesignSystem.Colors.primary)
                )
                .foregroundColor(.white)
            }
            .disabled(contentBlockerManager.isUpdating)
            .opacity(contentBlockerManager.isUpdating ? 0.6 : 1.0)
        }
        .padding(AppDesignSystem.Spacing.cardPadding)
        .appCardStyle()
        .shadow(
            color: AppDesignSystem.Shadow.card.color,
            radius: AppDesignSystem.Shadow.card.radius,
            x: AppDesignSystem.Shadow.card.offset.width,
            y: AppDesignSystem.Shadow.card.offset.height
        )
    }
    
    // MARK: - 统计信息卡片组
    private var statisticsCardsGrid: some View {
        VStack(spacing: AppDesignSystem.Spacing.md) {
            // 第一行：规则统计
            HStack(spacing: AppDesignSystem.Spacing.md) {
                StatisticCard(
                    title: "已编译规则", 
                    value: "\(contentBlockerManager.statistics.totalRulesLoaded.formatted())",
                    subtitle: "条活跃规则",
                    icon: "list.bullet.rectangle",
                    color: AppDesignSystem.Colors.primary
                )
                
                StatisticCard(
                    title: "活跃列表", 
                    value: "\(contentBlockerManager.statistics.activeFilterLists.count)",
                    subtitle: "个过滤列表",
                    icon: "doc.text.fill",
                    color: AppDesignSystem.Colors.accent
                )
            }
            
            // 第二行：效果统计
            HStack(spacing: AppDesignSystem.Spacing.md) {
                StatisticCard(
                    title: "已屏蔽请求", 
                    value: "\(contentBlockerManager.statistics.blockedRequestsCount.formatted())",
                    subtitle: "个广告请求",
                    icon: "xmark.shield.fill",
                    color: AppDesignSystem.Colors.success
                )
                
                StatisticCard(
                    title: "内存使用", 
                    value: ByteCountFormatter.string(fromByteCount: Int64(contentBlockerManager.statistics.memoryUsage), countStyle: .memory),
                    subtitle: "规则占用",
                    icon: "memorychip.fill",
                    color: AppDesignSystem.Colors.warning
                )
            }
            
            // 最后更新时间
            if let lastUpdate = contentBlockerManager.statistics.lastUpdateDate {
                HStack {
                    Image(systemName: "clock.fill")
                        .foregroundColor(AppDesignSystem.Colors.iconSecondary)
                        .font(.system(size: 14))
                    
                    Text("最后更新：\(lastUpdate.formatted(date: .abbreviated, time: .shortened))")
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                    
                    Spacer()
                }
                .padding(.top, AppDesignSystem.Spacing.xs)
            }
        }
        .padding(AppDesignSystem.Spacing.cardPadding)
        .appCardStyle()
        .shadow(
            color: AppDesignSystem.Shadow.card.color,
            radius: AppDesignSystem.Shadow.card.radius,
            x: AppDesignSystem.Shadow.card.offset.width,
            y: AppDesignSystem.Shadow.card.offset.height
        )
    }
    
    // MARK: - 统计卡片组件
    private struct StatisticCard: View {
        let title: String
        let value: String
        let subtitle: String
        let icon: String
        let color: Color
        
        var body: some View {
            VStack(spacing: AppDesignSystem.Spacing.sm) {
                HStack {
                    Image(systemName: icon)
                        .font(.system(size: 20, weight: .semibold))
                        .foregroundColor(color)
                    
                    Spacer()
                }
                
                VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                    Text(value)
                        .font(.system(size: 24, weight: .bold, design: .rounded))
                        .foregroundColor(AppDesignSystem.Colors.textPrimary)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text(title)
                            .font(AppDesignSystem.Typography.caption1)
                            .foregroundColor(AppDesignSystem.Colors.textSecondary)
                        
                        Text(subtitle)
                            .font(AppDesignSystem.Typography.caption2)
                            .foregroundColor(AppDesignSystem.Colors.textTertiary)
                    }
                }
                
                Spacer()
            }
            .frame(maxWidth: .infinity, minHeight: 100)
            .padding(AppDesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.md)
                    .fill(AppDesignSystem.Colors.backgroundSecondary)
            )
            .overlay(
                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.md)
                    .stroke(color.opacity(0.2), lineWidth: 1)
            )
        }
    }
    
    // MARK: - 手动更新过滤列表区域
    private var manualUpdateSection: some View {
        Button(action: updateFilterLists) {
            HStack(spacing: AppDesignSystem.Spacing.md) {
                ZStack {
                    RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.sm)
                        .fill(AppDesignSystem.Colors.primary.opacity(0.1))
                        .frame(width: 32, height: 32)
                    
                    Image(systemName: contentBlockerManager.isUpdating ? "arrow.circlepath" : "arrow.clockwise")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppDesignSystem.Colors.primary)
                        .rotationEffect(.degrees(contentBlockerManager.isUpdating ? 360 : 0))
                        .animation(contentBlockerManager.isUpdating ? .linear(duration: 1).repeatForever(autoreverses: false) : .default, value: contentBlockerManager.isUpdating)
                }
                
                VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                    Text("手动更新订阅规则")
                        .font(AppDesignSystem.Typography.body)
                        .foregroundColor(AppDesignSystem.Colors.textPrimary)
                    
                    Text(contentBlockerManager.isUpdating ? "正在下载和编译..." : "重新下载最新规则并编译应用")
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                if contentBlockerManager.isUpdating {
                    ProgressView()
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "icloud.and.arrow.down")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppDesignSystem.Colors.primary)
                }
            }
            .padding(.vertical, AppDesignSystem.Spacing.sm)
        }
        .disabled(contentBlockerManager.isUpdating)
        .opacity(contentBlockerManager.isUpdating ? 0.6 : 1.0)
    }
    
    // MARK: - 广告屏蔽测试区域
    private var adBlockTestSection: some View {
        NavigationLink(destination: AdBlockTestView()) {
            HStack(spacing: AppDesignSystem.Spacing.md) {
                ZStack {
                    RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.sm)
                        .fill(AppDesignSystem.Colors.accent.opacity(0.1))
                        .frame(width: 40, height: 40)
                    
                    Image(systemName: "testtube.2")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(AppDesignSystem.Colors.accent)
                }
                
                VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                    Text("广告屏蔽测试")
                        .font(AppDesignSystem.Typography.body)
                        .foregroundColor(AppDesignSystem.Colors.textPrimary)
                    
                    Text("使用 adblock-tester.com 检测当前屏蔽效果")
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppDesignSystem.Colors.iconSecondary)
            }
            .padding(AppDesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.md)
                    .fill(AppDesignSystem.Colors.backgroundSecondary)
            )
            .overlay(
                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.md)
                    .stroke(AppDesignSystem.Colors.accent.opacity(0.2), lineWidth: 1)
            )
        }
    }
    
    // MARK: - 高级设置内容
    private var advancedSettingsContent: some View {
        VStack(spacing: AppDesignSystem.Spacing.md) {
            // SafariConverterLib集成状态
            VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.sm) {
                HStack {
                    Image(systemName: EasyListConverter.isSafariConverterLibAvailable ? "checkmark.circle.fill" : "xmark.circle.fill")
                        .foregroundColor(EasyListConverter.isSafariConverterLibAvailable ? AppDesignSystem.Colors.success : AppDesignSystem.Colors.error)
                        .font(.system(size: 18, weight: .medium))
                    
                    Text("SafariConverterLib")
                        .font(AppDesignSystem.Typography.headline)
                        .foregroundColor(AppDesignSystem.Colors.textPrimary)
                    
                    Spacer()
                    
                    Text(EasyListConverter.safariConverterLibVersion)
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                }
                
                Text(EasyListConverter.isSafariConverterLibAvailable ? 
                     "已集成 AdGuard 专业转换器，支持完整 EasyList 语法" : 
                     "使用本地转换器实现，功能有限")
                    .font(AppDesignSystem.Typography.callout)
                    .foregroundColor(AppDesignSystem.Colors.textSecondary)
                
                if !EasyListConverter.isSafariConverterLibAvailable {
                    Button("查看集成指南") {
                        showIntegrationGuide = true
                    }
                    .font(AppDesignSystem.Typography.callout)
                    .foregroundColor(AppDesignSystem.Colors.primary)
                }
            }
            .padding(AppDesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.md)
                    .fill(AppDesignSystem.Colors.backgroundTertiary)
            )
            
            // 其他高级设置
            VStack(spacing: AppDesignSystem.Spacing.lg) {
                // 详细日志记录
                VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                    Toggle("详细日志记录", isOn: Binding(
                        get: { contentBlockerManager.configuration.detailedLogging },
                        set: { newValue in
                            contentBlockerManager.configuration.detailedLogging = newValue
                        }
                    ))
                    .font(AppDesignSystem.Typography.body)
                    
                    Text("启用详细的调试日志，有助于诊断问题，但会增加存储使用")
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                }
                
                // 启用高级屏蔽
                VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                    Toggle("启用高级屏蔽", isOn: Binding(
                        get: { contentBlockerManager.configuration.enableAdvancedBlocking },
                        set: { newValue in
                            contentBlockerManager.configuration.enableAdvancedBlocking = newValue
                        }
                    ))
                    .font(AppDesignSystem.Typography.body)
                    
                    Text("使用SafariConverterLib的完整功能，支持复杂规则语法，但会增加内存使用")
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                }
                
                // 性能优化模式
                VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                    Toggle("性能优化模式", isOn: Binding(
                        get: { contentBlockerManager.configuration.optimizeForPerformance },
                        set: { newValue in
                            contentBlockerManager.configuration.optimizeForPerformance = newValue
                        }
                    ))
                    .font(AppDesignSystem.Typography.body)
                    
                    Text("优化规则以降低CPU使用，可能会轻微影响屏蔽覆盖范围")
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                }
                
                // 自动降级策略
                VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                    Toggle("自动降级策略", isOn: Binding(
                        get: { contentBlockerManager.configuration.enableAutomaticFallback },
                        set: { newValue in
                            contentBlockerManager.configuration.enableAutomaticFallback = newValue
                        }
                    ))
                    .font(AppDesignSystem.Typography.body)
                    
                    Text("编译失败时自动使用精简规则集，确保基本屏蔽功能正常")
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                }
                
                // 规则数量限制
                VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                    HStack {
                        Text("每批规则数量限制")
                            .font(AppDesignSystem.Typography.body)
                        
                        Spacer()
                        
                        Text("\(contentBlockerManager.configuration.maxRulesPerList)")
                            .font(AppDesignSystem.Typography.body)
                            .foregroundColor(AppDesignSystem.Colors.textSecondary)
                    }
                    
                    Slider(
                        value: Binding(
                            get: { Double(contentBlockerManager.configuration.maxRulesPerList) },
                            set: { newValue in
                                contentBlockerManager.configuration.maxRulesPerList = Int(newValue)
                            }
                        ),
                        in: 10000...100000,
                        step: 5000
                    )
                    .accentColor(AppDesignSystem.Colors.primary)
                    
                    Text("单个规则列表的最大规则数量，较小值更稳定但可能需要分批处理")
                        .font(AppDesignSystem.Typography.caption1)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                }
            }
        }
    }
    
    // MARK: - 集成指南弹窗
    private var integrationGuideSheet: some View {
        NavigationView {
            VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.lg) {
                Text("SafariConverterLib 集成指南")
                    .font(AppDesignSystem.Typography.title1)
                    .foregroundColor(AppDesignSystem.Colors.textPrimary)
                    .padding(.bottom, AppDesignSystem.Spacing.md)
                
                ScrollView {
                    Text(EasyListConverter.integrationGuide)
                        .font(.system(.body, design: .monospaced))
                        .foregroundColor(AppDesignSystem.Colors.textPrimary)
                        .padding(AppDesignSystem.Spacing.md)
                        .background(AppDesignSystem.Colors.backgroundTertiary)
                        .cornerRadius(AppDesignSystem.CornerRadius.md)
                }
                
                Spacer()
            }
            .padding(AppDesignSystem.Spacing.lg)
            .navigationTitle("集成指南")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        showIntegrationGuide = false
                    }
                    .font(AppDesignSystem.Typography.buttonLabel)
                    .foregroundColor(AppDesignSystem.Colors.primary)
                }
            }
        }
    }
    
    // MARK: - 私有方法
    
    private func toggleContentBlocking() {
        Task {
            do {
                if contentBlockerManager.isEnabled {
                    contentBlockerManager.disableContentBlocking()
                } else {
                    try await contentBlockerManager.enableContentBlocking()
                }
                logger.info("广告屏蔽状态切换: \(contentBlockerManager.isEnabled)")
            } catch {
                updateError = error
                showingUpdateAlert = true
                logger.error("广告屏蔽切换失败: \(error.localizedDescription)")
            }
        }
    }
    
    private func toggleFilterList(_ listType: FilterListType, enabled: Bool) {
        if enabled {
            contentBlockerManager.configuration.enabledFilterLists.insert(listType)
        } else {
            contentBlockerManager.configuration.enabledFilterLists.remove(listType)
        }
        
        // 自动更新配置
        Task {
            try? await contentBlockerManager.updateFilterLists()
        }
        
        logger.info("过滤列表切换: \(listType.displayName) = \(enabled)")
    }
    
    private func updateFilterLists() {
        Task {
            do {
                try await contentBlockerManager.updateFilterLists()
                updateError = nil
                showingUpdateAlert = true
                logger.info("过滤列表更新成功")
            } catch {
                updateError = error
                showingUpdateAlert = true
                logger.error("过滤列表更新失败: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - 过滤列表行组件
struct FilterListRow: View {
    let listType: FilterListType
    let isEnabled: Bool
    let onToggle: (Bool) -> Void
    
    var body: some View {
        HStack(spacing: AppDesignSystem.Spacing.md) {
            // 列表图标
            ZStack {
                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.sm)
                    .fill(isEnabled ? AppDesignSystem.Colors.primary : AppDesignSystem.Colors.backgroundTertiary)
                    .frame(width: 40, height: 40)
                
                Image(systemName: getIconForListType())
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(isEnabled ? .white : AppDesignSystem.Colors.iconSecondary)
            }
            
            // 列表信息
            VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                Text(listType.displayName)
                    .font(AppDesignSystem.Typography.body)
                    .foregroundColor(AppDesignSystem.Colors.textPrimary)
                
                Text("支持最多 \(listType.maxRules.formatted()) 条规则")
                    .font(AppDesignSystem.Typography.caption1)
                    .foregroundColor(AppDesignSystem.Colors.textSecondary)
            }
            
            Spacer()
            
            // 状态指示器和切换开关
            VStack(alignment: .trailing, spacing: AppDesignSystem.Spacing.xs) {
                Toggle("", isOn: Binding(
                    get: { isEnabled },
                    set: onToggle
                ))
                .labelsHidden()
                
                if isEnabled {
                    Text("已启用")
                        .font(AppDesignSystem.Typography.caption2)
                        .foregroundColor(AppDesignSystem.Colors.success)
                } else {
                    Text("已禁用")
                        .font(AppDesignSystem.Typography.caption2)
                        .foregroundColor(AppDesignSystem.Colors.textTertiary)
                }
            }
        }
        .padding(.vertical, AppDesignSystem.Spacing.sm)
        .animation(AppDesignSystem.Animation.quick, value: isEnabled)
    }
    
    private func getIconForListType() -> String {
        switch listType {
        case .easyList:
            return "shield.fill"
        case .easyPrivacy:
            return "eye.slash.fill"
        case .fanboyAnnoyances:
            return "xmark.rectangle.fill"
        case .easyListChina:
            return "location.fill"
        }
    }
}

// MARK: - 统计信息视图
struct ContentBlockerStatisticsView: View {
    @StateObject private var contentBlockerManager = ContentBlockerManager.shared
    
    var body: some View {
        List {
            Section("当前状态") {
                StatisticRow(
                    title: "状态",
                    value: contentBlockerManager.isEnabled ? "已启用" : "已禁用",
                    icon: "shield"
                )
                
                StatisticRow(
                    title: "已加载规则",
                    value: "\(contentBlockerManager.statistics.totalRulesLoaded)",
                    icon: "list.bullet"
                )
                
                StatisticRow(
                    title: "活跃列表",
                    value: "\(contentBlockerManager.statistics.activeFilterLists.count)",
                    icon: "doc.text"
                )
            }
            
            Section("性能指标") {
                StatisticRow(
                    title: "内存使用",
                    value: ByteCountFormatter.string(fromByteCount: Int64(contentBlockerManager.statistics.memoryUsage), countStyle: .memory),
                    icon: "memorychip"
                )
                
                StatisticRow(
                    title: "已屏蔽请求",
                    value: "\(contentBlockerManager.statistics.blockedRequestsCount)",
                    icon: "xmark.shield"
                )
            }
            
            Section("更新信息") {
                StatisticRow(
                    title: "最后更新",
                    value: contentBlockerManager.statistics.lastUpdateDate?.formatted(date: .abbreviated, time: .shortened) ?? "未知",
                    icon: "clock"
                )
            }
        }
        .navigationTitle("统计信息")
        .navigationBarTitleDisplayMode(.large)
    }
}

// MARK: - 统计行
struct StatisticRow: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 24)
            
            Text(title)
            
            Spacer()
            
            Text(value)
                .foregroundColor(.secondary)
        }
    }
}

#Preview {
    NavigationView {
        ContentBlockerSettingsView()
    }
} 