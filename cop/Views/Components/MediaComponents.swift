import SwiftUI
import AVKit
import UIKit
import CoreMedia
import ImageIO

// MARK: - 媒体卡片组件
struct MediaCard: View {
    let mediaFile: MediaFileInfo
    let size: CGSize
    let onTap: () -> Void
    let onLongPress: (() -> Void)?
    
    @State private var isPressed = false
    @State private var thumbnail: UIImage?
    
    init(mediaFile: MediaFileInfo, size: CGSize = CGSize(width: 160, height: 200), onTap: @escaping () -> Void, onLongPress: (() -> Void)? = nil) {
        self.mediaFile = mediaFile
        self.size = size
        self.onTap = onTap
        self.onLongPress = onLongPress
    }
    
    var body: some View {
        VStack(spacing: AppDesignSystem.Spacing.sm) {
            // 缩略图区域
            ZStack {
                // 背景占位
                RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.thumbnail)
                    .fill(AppDesignSystem.Colors.backgroundSecondary)
                    .frame(width: size.width, height: size.width)
                
                // 异步缩略图
                AsyncThumbnailView(mediaFile: mediaFile, size: CGSize(width: size.width, height: size.width))
                    .cornerRadius(AppDesignSystem.CornerRadius.thumbnail)
                
                // 媒体类型覆盖层
                VStack {
                    HStack {
                        Spacer()
                        MediaTypeOverlay(mediaType: mediaFile.type, duration: mediaFile.formattedDuration)
                    }
                    Spacer()
                }
                .padding(AppDesignSystem.Spacing.sm)
            }
            
            // 文件信息
            VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                // 固定2行高度的文件名区域
                Text(mediaFile.name)
                    .font(AppDesignSystem.Typography.caption1)
                    .fontWeight(.medium)
                    .foregroundColor(AppDesignSystem.Colors.text)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                    .frame(height: calculateTextHeight(), alignment: .top)

                Text(mediaFile.formattedFileSize)
                    .font(AppDesignSystem.Typography.caption2)
                    .foregroundColor(AppDesignSystem.Colors.textSecondary)
            }
            .frame(width: size.width, alignment: .leading)
        }
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(AppDesignSystem.Animation.quick, value: isPressed)
        .onTapGesture {
            onTap()
        }
        .onLongPressGesture(
            minimumDuration: 0.5,
            maximumDistance: 10,
            pressing: { pressing in
                isPressed = pressing
            },
            perform: {
                onLongPress?()
            }
        )
    }

    // 计算固定的2行文本高度
    private func calculateTextHeight() -> CGFloat {
        let font = UIFont.systemFont(ofSize: 12, weight: .medium) // caption1 对应的字体
        let lineHeight = font.lineHeight
        let lineSpacing: CGFloat = 2 // 行间距
        return lineHeight * 2 + lineSpacing
    }
}

// MARK: - 异步缩略图视图 (OPTIMIZED)
struct AsyncThumbnailView: View {
    let mediaFile: MediaFileInfo
    let size: CGSize

    @State private var thumbnail: UIImage?
    @State private var isLoading = true
    @State private var loadingFailed = false

    // OPTIMIZED: 使用优化的三级缓存系统
    private let thumbnailCache = OptimizedThumbnailCache.shared
    
    var body: some View {
        ZStack {
            if let thumbnail = thumbnail {
                Image(uiImage: thumbnail)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: size.width, height: size.height)
                    .clipped()
            } else if loadingFailed {
                // 加载失败状态
                VStack(spacing: AppDesignSystem.Spacing.xs) {
                    Image(systemName: mediaFile.type == .video ? "video.slash" : "photo")
                        .font(.system(size: size.width * 0.3))
                        .foregroundColor(AppDesignSystem.Colors.iconSecondary)
                    
                    Text("无法加载")
                        .font(.caption2)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)
                }
                .frame(width: size.width, height: size.height)
            } else if isLoading {
                // 加载中状态
                ProgressView()
                    .scaleEffect(1.2)
                    .frame(width: size.width, height: size.height)
                    .background(AppDesignSystem.Colors.backgroundSecondary)
            }
        }
        .onAppear {
            loadThumbnail()
        }
        .onChange(of: mediaFile.id) {
            // 当媒体文件ID变化时重新加载缩略图
            thumbnail = nil
            isLoading = true
            loadingFailed = false
            loadThumbnail()
        }
    }
    
    // OPTIMIZED: 使用三级缓存系统加载缩略图
    private func loadThumbnail() {
        Task {
            // 首先尝试从缓存加载
            if let cachedThumbnail = await thumbnailCache.loadThumbnail(for: mediaFile.id, size: size) {
                await MainActor.run {
                    self.thumbnail = cachedThumbnail
                    self.isLoading = false
                }
                return
            }

            // 缓存未命中，生成新缩略图
            do {
                let thumbnailImage = try await generateThumbnail(for: mediaFile)

                // 存储到缓存
                await thumbnailCache.storeThumbnail(thumbnailImage, for: mediaFile.id, size: size)

                await MainActor.run {
                    self.thumbnail = thumbnailImage
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.loadingFailed = true
                    self.isLoading = false
                }
            }
        }
    }
    
    private func generateThumbnail(for mediaFile: MediaFileInfo) async throws -> UIImage {
        // 首先尝试从缓存的缩略图URL加载
        if let thumbnailURL = mediaFile.thumbnailURL,
           FileManager.default.fileExists(atPath: thumbnailURL.path) {
            if let imageData = try? Data(contentsOf: thumbnailURL),
               let image = UIImage(data: imageData) {
                return image
            }
        }

        // 如果缓存的缩略图不存在，尝试从原文件生成
        return try await generateThumbnailFromOriginalFile(mediaFile)
    }
    
    private func generateThumbnailFromOriginalFile(_ mediaFile: MediaFileInfo) async throws -> UIImage {
        let localURL = mediaFile.localURL
        let thumbnailSize = CGSize(width: 300, height: 300)
        
        switch mediaFile.type {
        case .image:
            return try await generateImageThumbnail(from: localURL, size: thumbnailSize)
        case .video:
            return try await generateVideoThumbnail(from: localURL, size: thumbnailSize)
        }
    }
    
    private func generateImageThumbnail(from sourceURL: URL, size: CGSize) async throws -> UIImage {
        guard let imageSource = CGImageSourceCreateWithURL(sourceURL as CFURL, nil) else {
            throw ThumbnailError.generationFailed
        }
        
        let options: [CFString: Any] = [
            kCGImageSourceCreateThumbnailFromImageIfAbsent: true,
            kCGImageSourceCreateThumbnailWithTransform: true,
            kCGImageSourceThumbnailMaxPixelSize: max(size.width, size.height)
        ]
        
        guard let thumbnail = CGImageSourceCreateThumbnailAtIndex(imageSource, 0, options as CFDictionary) else {
            throw ThumbnailError.generationFailed
        }
        
        return UIImage(cgImage: thumbnail)
    }
    
    private func generateVideoThumbnail(from sourceURL: URL, size: CGSize) async throws -> UIImage {
        let asset = AVURLAsset(url: sourceURL)
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        imageGenerator.maximumSize = size

        // 使用第3秒的时间点，与全屏缩略图栏保持一致
        let time = CMTime(seconds: 3.0, preferredTimescale: 600)

        do {
            let cgImage = try await imageGenerator.image(at: time).image
            return UIImage(cgImage: cgImage)
        } catch {
            // 如果第3秒失败，尝试第1秒
            let fallbackTime = CMTime(seconds: 1.0, preferredTimescale: 600)
            do {
                let cgImage = try await imageGenerator.image(at: fallbackTime).image
                return UIImage(cgImage: cgImage)
            } catch {
                throw ThumbnailError.generationFailed
            }
        }
    }
}

// MARK: - 缩略图错误类型
enum ThumbnailError: Error {
    case generationFailed
    case fileNotFound
}

// MARK: - 媒体类型覆盖层
struct MediaTypeOverlay: View {
    let mediaType: MediaType
    let duration: String?
    
    var body: some View {
        Group {
            switch mediaType {
            case .video:
                if let duration = duration {
                    HStack(spacing: AppDesignSystem.Spacing.xs) {
                        Image(systemName: "play.circle.fill")
                            .font(.caption)
                        Text(duration)
                            .font(.caption2)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, AppDesignSystem.Spacing.sm)
                    .padding(.vertical, AppDesignSystem.Spacing.xs)
                    .background(Color.black.opacity(0.7))
                    .cornerRadius(AppDesignSystem.CornerRadius.xs)
                }
            case .image:
                // 图片类型不需要覆盖层，或者可以显示其他信息
                EmptyView()
            }
        }
    }
}

// MARK: - 媒体类型指示器
struct MediaTypeIndicator: View {
    let mediaType: MediaType
    let duration: String?
    
    var body: some View {
        HStack(spacing: AppDesignSystem.Spacing.xs) {
            Image(systemName: iconName)
                .font(.caption)
                .foregroundColor(iconColor)
            
            if let duration = duration, mediaType == .video {
                Text(duration)
                    .font(.caption2)
                    .foregroundColor(AppDesignSystem.Colors.textSecondary)
            }
        }
        .padding(.horizontal, AppDesignSystem.Spacing.sm)
        .padding(.vertical, AppDesignSystem.Spacing.xs)
        .background(backgroundColor)
        .cornerRadius(AppDesignSystem.CornerRadius.xs)
    }
    
    private var iconName: String {
        switch mediaType {
        case .video: return "video.fill"
        case .image: return "photo.fill"
        }
    }
    
    private var iconColor: Color {
        switch mediaType {
        case .video: return AppDesignSystem.Colors.accent
        case .image: return AppDesignSystem.Colors.success
        }
    }
    
    private var backgroundColor: Color {
        switch mediaType {
        case .video: return AppDesignSystem.Colors.accent.opacity(0.1)
        case .image: return AppDesignSystem.Colors.success.opacity(0.1)
        }
    }
}

// MARK: - 现代化上下文菜单
struct ModernMediaContextMenu: View {
    let mediaFile: MediaFileInfo
    let viewModel: MediaLibraryViewModel
    
    var body: some View {
        VStack {
            AppButton("查看详情", icon: "info.circle") {
                // 显示详情
            }
            
            AppButton("分享", icon: "square.and.arrow.up") {
                // 分享功能
            }
            
            AppButton("导出", icon: "square.and.arrow.down") {
                // 导出功能
            }
            
            Divider()
            
            AppButton("删除", icon: "trash", style: .destructive) {
                // 删除功能
            }
        }
    }
}

// MARK: - 网格浏览器组件（优化版）
struct ModernGridBrowser: View {
    let mediaFiles: [MediaFileInfo]
    let viewModel: MediaLibraryViewModel
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                LazyVGrid(
                    columns: adaptiveColumns(for: geometry.size.width),
                    spacing: AppDesignSystem.Layout.gridSpacing
                ) {
                    ForEach(Array(mediaFiles.enumerated()), id: \.element.id) { index, mediaFile in
                        MediaCard(
                            mediaFile: mediaFile,
                            size: calculateCardSize(for: geometry.size.width)
                        ) {
                            viewModel.showFullScreenViewer(
                                mediaFiles: mediaFiles,
                                at: index,
                                initialUIState: mediaFile.type == .video ? .hidden : .visible
                            )
                        } onLongPress: {
                            // 长按操作
                        }
                        .contextMenu {
                            ModernMediaContextMenu(mediaFile: mediaFile, viewModel: viewModel)
                        }
                    }
                }
                .padding(.horizontal, AppDesignSystem.Spacing.lg)
                .padding(.vertical, AppDesignSystem.Spacing.md)
            }
        }
    }
    
    private func adaptiveColumns(for width: CGFloat) -> [GridItem] {
        let availableWidth = width - (AppDesignSystem.Spacing.lg * 2) // 减去左右边距
        let minCardWidth: CGFloat = 160
        let spacing = AppDesignSystem.Layout.gridSpacing
        
        // 计算可容纳的列数
        let columnsCount = max(2, Int((availableWidth + spacing) / (minCardWidth + spacing)))
        
        // 创建等宽的列
        return Array(repeating: GridItem(.flexible(), spacing: spacing), count: columnsCount)
    }
    
    private func calculateCardSize(for width: CGFloat) -> CGSize {
        let availableWidth = width - (AppDesignSystem.Spacing.lg * 2)
        let columnsCount = adaptiveColumns(for: width).count
        let totalSpacing = AppDesignSystem.Layout.gridSpacing * CGFloat(columnsCount - 1)
        let cardWidth = (availableWidth - totalSpacing) / CGFloat(columnsCount)
        
        return CGSize(width: cardWidth, height: cardWidth * 1.4)
    }
}

// MARK: - 列表浏览器组件
struct ModernListBrowser: View {
    let mediaFiles: [MediaFileInfo]
    let viewModel: MediaLibraryViewModel
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: AppDesignSystem.Spacing.xs) {
                ForEach(Array(mediaFiles.enumerated()), id: \.element.id) { index, mediaFile in
                    MediaListRow(mediaFile: mediaFile) {
                        viewModel.showFullScreenViewer(
                            mediaFiles: mediaFiles,
                            at: index,
                            initialUIState: mediaFile.type == .video ? .hidden : .visible
                        )
                    }
                    .contextMenu {
                        ModernMediaContextMenu(mediaFile: mediaFile, viewModel: viewModel)
                    }
                }
            }
            .padding(.horizontal, AppDesignSystem.Spacing.lg)
        }
    }
}

// MARK: - 媒体列表行
struct MediaListRow: View {
    let mediaFile: MediaFileInfo
    let onTap: () -> Void
    
    var body: some View {
        AppCard(padding: AppDesignSystem.Spacing.md, hasHover: true) {
            HStack(spacing: AppDesignSystem.Spacing.lg) {
                // 缩略图
                AsyncThumbnailView(mediaFile: mediaFile, size: CGSize(width: 60, height: 60))
                    .cornerRadius(AppDesignSystem.CornerRadius.sm)
                
                // 文件信息
                VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                    Text(mediaFile.name)
                        .font(AppDesignSystem.Typography.bodyEmphasized)
                        .foregroundColor(AppDesignSystem.Colors.text)
                        .lineLimit(1)
                    
                    HStack(spacing: AppDesignSystem.Spacing.md) {
                        MediaTypeIndicator(mediaType: mediaFile.type, duration: mediaFile.formattedDuration)
                        
                        Text(mediaFile.formattedFileSize)
                            .font(AppDesignSystem.Typography.caption1)
                            .foregroundColor(AppDesignSystem.Colors.textSecondary)
                        
                        Spacer()
                        
                        Text(mediaFile.formattedDateCreated ?? "")
                            .font(AppDesignSystem.Typography.caption2)
                            .foregroundColor(AppDesignSystem.Colors.textTertiary)
                    }
                }
                
                Spacer()
                
                // 操作指示器
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(AppDesignSystem.Colors.iconSecondary)
            }
        }
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - 瀑布流浏览器组件（真正的瀑布流）
struct ModernWaterfallBrowser: View {
    let mediaFiles: [MediaFileInfo]
    let viewModel: MediaLibraryViewModel

    @State private var columnHeights: [CGFloat] = []
    
    private let columnCount = 2
    private let spacing: CGFloat = AppDesignSystem.Layout.gridSpacing
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView {
                WaterfallLayout(
                    mediaFiles: mediaFiles,
                    columnCount: columnCount,
                    spacing: spacing,
                    containerWidth: geometry.size.width
                ) { index, mediaFile in
                    MediaWaterfallCard(
                        mediaFile: mediaFile,
                        width: calculateCardWidth(for: geometry.size.width)
                    ) {
                        viewModel.showFullScreenViewer(
                            mediaFiles: mediaFiles,
                            at: index,
                            initialUIState: mediaFile.type == .video ? .hidden : .visible
                        )
                    }
                    .contextMenu {
                        ModernMediaContextMenu(mediaFile: mediaFile, viewModel: viewModel)
                    }
                }
                .padding(.horizontal, AppDesignSystem.Spacing.lg)
                .padding(.vertical, AppDesignSystem.Spacing.md)
            }
        }
    }
    
    private func calculateCardWidth(for containerWidth: CGFloat) -> CGFloat {
        let availableWidth = containerWidth - (AppDesignSystem.Spacing.lg * 2)
        let totalSpacing = spacing * CGFloat(columnCount - 1)
        return (availableWidth - totalSpacing) / CGFloat(columnCount)
    }
}

// MARK: - 瀑布流布局容器
struct WaterfallLayout<Content: View>: View {
    let mediaFiles: [MediaFileInfo]
    let columnCount: Int
    let spacing: CGFloat
    let containerWidth: CGFloat
    let content: (Int, MediaFileInfo) -> Content
    
    @State private var columnHeights: [CGFloat] = []
    
    var body: some View {
        let cardWidth = calculateCardWidth()
        
        HStack(alignment: .top, spacing: spacing) {
            ForEach(0..<columnCount, id: \.self) { columnIndex in
                LazyVStack(spacing: spacing) {
                    ForEach(itemsForColumn(columnIndex), id: \.element.id) { indexAndFile in
                        content(indexAndFile.index, indexAndFile.element)
                    }
                }
                .frame(width: cardWidth)
            }
        }
        .onAppear {
            columnHeights = Array(repeating: 0, count: columnCount)
        }
    }
    
    private func calculateCardWidth() -> CGFloat {
        let availableWidth = containerWidth - (AppDesignSystem.Spacing.lg * 2)
        let totalSpacing = spacing * CGFloat(columnCount - 1)
        return (availableWidth - totalSpacing) / CGFloat(columnCount)
    }
    
    private func itemsForColumn(_ columnIndex: Int) -> [(index: Int, element: MediaFileInfo)] {
        return mediaFiles.enumerated().compactMap { index, mediaFile in
            // 简单的分配策略：按索引取模分配到不同列
            if index % columnCount == columnIndex {
                return (index: index, element: mediaFile)
            }
            return nil
        }
    }
}

// MARK: - 瀑布流媒体卡片
struct MediaWaterfallCard: View {
    let mediaFile: MediaFileInfo
    let width: CGFloat
    let onTap: () -> Void
    
    @State private var imageHeight: CGFloat = 200
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // 异步缩略图
            AsyncThumbnailView(
                mediaFile: mediaFile,
                size: CGSize(width: width, height: imageHeight)
            )
            .frame(width: width, height: imageHeight)
            .clipShape(RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.md))
            .overlay(
                // 媒体类型指示器
                VStack {
                    Spacer()
                    HStack {
                        MediaTypeOverlay(
                            mediaType: mediaFile.type,
                            duration: mediaFile.formattedDuration
                        )
                        Spacer()
                    }
                }
                .padding(AppDesignSystem.Spacing.sm)
            )
            .onAppear {
                // 根据图片实际尺寸计算高度
                calculateImageHeight()
            }
            
            // 文件信息
            VStack(alignment: .leading, spacing: AppDesignSystem.Spacing.xs) {
                // 固定2行高度的文件名区域
                Text(mediaFile.name)
                    .font(AppDesignSystem.Typography.caption1)
                    .fontWeight(.medium)
                    .foregroundColor(AppDesignSystem.Colors.text)
                    .lineLimit(2)
                    .frame(height: calculateWaterfallTextHeight(), alignment: .top)

                HStack {
                    Text(mediaFile.formattedFileSize)
                        .font(AppDesignSystem.Typography.caption2)
                        .foregroundColor(AppDesignSystem.Colors.textSecondary)

                    Spacer()

                    Text(mediaFile.relativeTimeString)
                        .font(AppDesignSystem.Typography.caption2)
                        .foregroundColor(AppDesignSystem.Colors.textTertiary)
                }
            }
            .padding(AppDesignSystem.Spacing.sm)
        }
        .background(AppDesignSystem.Colors.backgroundSecondary)
        .cornerRadius(AppDesignSystem.CornerRadius.lg)
        .overlay(
            RoundedRectangle(cornerRadius: AppDesignSystem.CornerRadius.lg)
                .stroke(AppDesignSystem.Colors.separator.opacity(0.2), lineWidth: 1)
        )
        .onTapGesture {
            onTap()
        }
    }
    
    private func calculateImageHeight() {
        // 根据图片的宽高比来计算高度
        if let dimensions = mediaFile.dimensions {
            let aspectRatio = dimensions.height / dimensions.width
            imageHeight = width * aspectRatio

            // 限制高度范围
            imageHeight = max(150, min(400, imageHeight))
        } else {
            // 默认高度，添加一些随机性以创建瀑布流效果
            let baseHeight: CGFloat = 180
            let variation = CGFloat.random(in: -30...60)
            imageHeight = baseHeight + variation
        }
    }

    // 计算瀑布流视图中固定的2行文本高度
    private func calculateWaterfallTextHeight() -> CGFloat {
        let font = UIFont.systemFont(ofSize: 12, weight: .medium) // caption1 对应的字体
        let lineHeight = font.lineHeight
        let lineSpacing: CGFloat = 2 // 行间距
        return lineHeight * 2 + lineSpacing
    }
} 